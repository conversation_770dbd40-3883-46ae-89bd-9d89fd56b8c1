"""
Workflow execution API routes
"""
from typing import Dict, Any
from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, HTTPException, status
from fastapi.responses import StreamingResponse
import json
import structlog

from app.core.dependencies import get_current_active_user
from app.models.user import User
from app.models.workflow import (
    Workflow,
    WorkflowExecution,
    WorkflowExecutionCreate,
    WorkflowExecutionResponse,
    NodeType
)
from app.services.workflow_service import workflow_service

logger = structlog.get_logger()

router = APIRouter()


@router.post("/{workflow_id}/execute", response_model=WorkflowExecutionResponse)
async def start_workflow_execution(
    workflow_id: str,
    execution_data: WorkflowExecutionCreate,
    current_user: User = Depends(get_current_active_user)
):
    """Start workflow execution (only if END node is connected)"""
    try:
        # Get workflow
        from bson import ObjectId
        try:
            workflow = await Workflow.find_one(
                Workflow.id == ObjectId(workflow_id),
                Workflow.user_id == str(current_user.id)
            )
        except Exception:
            workflow = None
        
        if not workflow:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Workflow not found"
            )
        
        # Validate workflow can be executed
        validation = await _validate_workflow_execution(workflow)
        if not validation["can_execute"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Cannot execute workflow: {', '.join(validation['errors'])}"
            )
        
        # Start execution
        execution = await workflow_service.start_workflow_execution(
            workflow_id=workflow_id,
            user=current_user,
            input_data=execution_data.input_data
        )
        
        logger.info(
            "Workflow execution started",
            execution_id=str(execution.id),
            workflow_id=workflow_id,
            user_id=str(current_user.id)
        )
        
        return execution.to_response()
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to start workflow execution", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to start workflow execution"
        )


@router.get("/{workflow_id}/execute/{execution_id}/stream")
async def stream_workflow_execution(
    workflow_id: str,
    execution_id: str,
    current_user: User = Depends(get_current_active_user)
):
    """Stream workflow execution progress"""
    try:
        # Get execution
        from bson import ObjectId
        try:
            execution = await WorkflowExecution.find_one(
                WorkflowExecution.id == ObjectId(execution_id),
                WorkflowExecution.workflow_id == workflow_id,
                WorkflowExecution.user_id == str(current_user.id)
            )
        except Exception:
            execution = None
        
        if not execution:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Execution not found"
            )
        
        async def generate_stream():
            try:
                # Extract input data from workflow nodes
                workflow = await Workflow.find_one(Workflow.id == workflow_id)
                input_data = _extract_input_data_from_nodes(workflow)
                
                async for update in workflow_service.execute_workflow_stream(execution, input_data):
                    yield f"data: {json.dumps(update)}\n\n"
                    
            except Exception as e:
                error_update = {
                    "type": "error",
                    "execution_id": execution_id,
                    "error": str(e)
                }
                yield f"data: {json.dumps(error_update)}\n\n"
        
        return StreamingResponse(
            generate_stream(),
            media_type="text/plain",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Content-Type": "text/event-stream"
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to stream workflow execution", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to stream workflow execution"
        )


@router.get("/{workflow_id}/executions", response_model=list[WorkflowExecutionResponse])
async def list_workflow_executions(
    workflow_id: str,
    current_user: User = Depends(get_current_active_user)
):
    """List workflow executions"""
    try:
        executions = await WorkflowExecution.find({
            "workflow_id": workflow_id,
            "user_id": str(current_user.id)
        }).sort([("started_at", -1)]).to_list()
        
        return [execution.to_response() for execution in executions]
        
    except Exception as e:
        logger.error("Failed to list executions", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to list executions"
        )


@router.get("/{workflow_id}/execute/{execution_id}", response_model=WorkflowExecutionResponse)
async def get_workflow_execution(
    workflow_id: str,
    execution_id: str,
    current_user: User = Depends(get_current_active_user)
):
    """Get workflow execution details"""
    try:
        execution = await WorkflowExecution.find_one({
            "_id": execution_id,
            "workflow_id": workflow_id,
            "user_id": str(current_user.id)
        })
        
        if not execution:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Execution not found"
            )
        
        return execution.to_response()
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to get execution", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get execution"
        )


@router.get("/{workflow_id}/execute/{execution_id}/state")
async def get_execution_state(
    workflow_id: str,
    execution_id: str,
    current_user: User = Depends(get_current_active_user)
):
    """Get current execution state from checkpoint"""
    try:
        execution = await WorkflowExecution.find_one({
            "_id": execution_id,
            "workflow_id": workflow_id,
            "user_id": str(current_user.id)
        })
        
        if not execution:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Execution not found"
            )
        
        state = await workflow_service.get_workflow_state(execution)
        return state or {"message": "No state available"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to get execution state", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get execution state"
        )


@router.post("/{workflow_id}/execute/{execution_id}/resume")
async def resume_workflow_execution(
    workflow_id: str,
    execution_id: str,
    checkpoint_id: str = None,
    current_user: User = Depends(get_current_active_user)
):
    """Resume workflow execution from checkpoint"""
    try:
        execution = await WorkflowExecution.find_one({
            "_id": execution_id,
            "workflow_id": workflow_id,
            "user_id": str(current_user.id)
        })
        
        if not execution:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Execution not found"
            )
        
        async def generate_stream():
            async for update in workflow_service.resume_workflow(execution, checkpoint_id):
                yield f"data: {json.dumps(update)}\n\n"
        
        return StreamingResponse(
            generate_stream(),
            media_type="text/plain",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Content-Type": "text/event-stream"
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to resume execution", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to resume execution"
        )


async def _validate_workflow_execution(workflow: Workflow) -> Dict[str, Any]:
    """Validate if workflow can be executed"""
    errors = []
    
    # Check if workflow has start and end nodes
    has_start = any(node.type == NodeType.START for node in workflow.nodes)
    has_end = any(node.type == NodeType.END for node in workflow.nodes)
    
    if not has_start:
        errors.append("Workflow must have a START node")
    if not has_end:
        errors.append("Workflow must have an END node")
    
    # Check if end node is connected
    end_nodes = [node for node in workflow.nodes if node.type == NodeType.END]
    is_end_connected = False
    if end_nodes:
        end_node_id = end_nodes[0].id
        is_end_connected = any(edge.target == end_node_id for edge in workflow.edges)
        if not is_end_connected:
            errors.append("END node must be connected to execute workflow")
    
    return {
        "can_execute": len(errors) == 0 and is_end_connected,
        "errors": errors
    }


def _extract_input_data_from_nodes(workflow: Workflow) -> Dict[str, Any]:
    """Extract input data from workflow nodes"""
    input_data = {}
    
    for node in workflow.nodes:
        if node.type == NodeType.UPLOAD_IMAGE and node.data:
            input_data["uploaded_image"] = node.data
        elif node.type == NodeType.PRODUCT_DETAILS and node.data:
            input_data["product_details"] = node.data
        elif node.type == NodeType.MODE_SELECTION and node.data:
            input_data["selected_mode"] = node.data
    
    return input_data
