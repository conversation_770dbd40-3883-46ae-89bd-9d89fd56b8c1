"""
Node management API routes - for canvas node operations
"""
from typing import Dict, Any, List
from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File
import structlog
import uuid
import os
from datetime import datetime

from app.core.dependencies import get_current_active_user
from app.core.config import settings
from app.models.user import User
from app.models.workflow import Workflow, NodeType
from app.models.image import ImageData, ImageSource, ImageMetadata, ImageFormat, ProductDetails

logger = structlog.get_logger()

router = APIRouter()


@router.post("/{workflow_id}/nodes/{node_id}/upload-image")
async def upload_image_to_node(
    workflow_id: str,
    node_id: str,
    file: UploadFile = File(...),
    current_user: User = Depends(get_current_active_user)
):
    """Upload image to upload_image node"""
    try:
        # Validate workflow and node
        from bson import ObjectId
        try:
            workflow = await Workflow.find_one(
                Workflow.id == ObjectId(workflow_id),
                Workflow.user_id == str(current_user.id)
            )
        except Exception:
            workflow = None
        
        if not workflow:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Workflow not found"
            )
        
        # Find the node
        node = next((n for n in workflow.nodes if n.id == node_id), None)
        if not node or node.type != NodeType.UPLOAD_IMAGE:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid upload image node"
            )
        
        # Validate file type
        if file.content_type not in settings.allowed_image_types:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid file type. Allowed: {', '.join(settings.allowed_image_types)}"
            )
        
        # Read file content
        content = await file.read()
        
        if len(content) > settings.max_file_size:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"File too large. Max size: {settings.max_file_size} bytes"
            )
        
        # Create unique filename
        file_extension = file.filename.split('.')[-1].lower()
        unique_filename = f"{uuid.uuid4()}.{file_extension}"
        
        # Save file
        os.makedirs(settings.upload_dir, exist_ok=True)
        file_path = os.path.join(settings.upload_dir, unique_filename)
        
        with open(file_path, "wb") as f:
            f.write(content)
        
        # Create image metadata
        metadata = ImageMetadata(
            width=0,  # Will be updated with actual dimensions
            height=0,
            format=ImageFormat(file_extension),
            size_bytes=len(content),
            mime_type=file.content_type
        )
        
        # Save image data
        image_data = ImageData(
            filename=unique_filename,
            original_filename=file.filename,
            source=ImageSource.UPLOAD,
            metadata=metadata,
            file_path=file_path,
            user_id=str(current_user.id)
        )
        await image_data.save()
        
        # Update node data
        for i, n in enumerate(workflow.nodes):
            if n.id == node_id:
                workflow.nodes[i].data = {
                    "image_id": str(image_data.id),
                    "filename": unique_filename,
                    "original_filename": file.filename,
                    "file_path": file_path,
                    "uploaded_at": datetime.utcnow().isoformat()
                }
                break
        
        await workflow.save()
        
        logger.info(
            "Image uploaded to node",
            workflow_id=workflow_id,
            node_id=node_id,
            filename=file.filename,
            user_id=str(current_user.id)
        )
        
        return {
            "message": "Image uploaded successfully",
            "image_id": str(image_data.id),
            "filename": unique_filename,
            "original_filename": file.filename
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to upload image", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to upload image"
        )


@router.post("/{workflow_id}/nodes/{node_id}/product-details")
async def update_product_details_node(
    workflow_id: str,
    node_id: str,
    product_details: ProductDetails,
    current_user: User = Depends(get_current_active_user)
):
    """Update product details in product_details node"""
    try:
        # Validate workflow and node
        from bson import ObjectId
        try:
            workflow = await Workflow.find_one(
                Workflow.id == ObjectId(workflow_id),
                Workflow.user_id == str(current_user.id)
            )
        except Exception:
            workflow = None
        
        if not workflow:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Workflow not found"
            )
        
        # Find the node
        node = next((n for n in workflow.nodes if n.id == node_id), None)
        if not node or node.type != NodeType.PRODUCT_DETAILS:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid product details node"
            )
        
        # Update node data
        for i, n in enumerate(workflow.nodes):
            if n.id == node_id:
                workflow.nodes[i].data = {
                    "name": product_details.name,
                    "description": product_details.description,
                    "purpose": product_details.purpose,
                    "mode": product_details.mode.value,
                    "additional_context": product_details.additional_context,
                    "updated_at": datetime.utcnow().isoformat()
                }
                break
        
        await workflow.save()
        
        logger.info(
            "Product details updated",
            workflow_id=workflow_id,
            node_id=node_id,
            user_id=str(current_user.id)
        )
        
        return {
            "message": "Product details updated successfully",
            "data": workflow.nodes[i].data
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to update product details", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update product details"
        )


@router.post("/{workflow_id}/nodes/{node_id}/mode-selection")
async def update_mode_selection_node(
    workflow_id: str,
    node_id: str,
    mode_data: Dict[str, Any],
    current_user: User = Depends(get_current_active_user)
):
    """Update mode selection in mode_selection node"""
    try:
        # Validate workflow and node
        from bson import ObjectId
        try:
            workflow = await Workflow.find_one(
                Workflow.id == ObjectId(workflow_id),
                Workflow.user_id == str(current_user.id)
            )
        except Exception:
            workflow = None
        
        if not workflow:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Workflow not found"
            )
        
        # Find the node
        node = next((n for n in workflow.nodes if n.id == node_id), None)
        if not node or node.type != NodeType.MODE_SELECTION:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid mode selection node"
            )
        
        # Validate mode
        valid_modes = ["with_model", "product_only"]
        selected_mode = mode_data.get("mode")
        
        if selected_mode not in valid_modes:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid mode. Must be one of: {', '.join(valid_modes)}"
            )
        
        # Update node data
        for i, n in enumerate(workflow.nodes):
            if n.id == node_id:
                workflow.nodes[i].data = {
                    "mode": selected_mode,
                    "model_preferences": mode_data.get("model_preferences", {}),
                    "style_preferences": mode_data.get("style_preferences", {}),
                    "updated_at": datetime.utcnow().isoformat()
                }
                break
        
        await workflow.save()
        
        logger.info(
            "Mode selection updated",
            workflow_id=workflow_id,
            node_id=node_id,
            mode=selected_mode,
            user_id=str(current_user.id)
        )
        
        return {
            "message": "Mode selection updated successfully",
            "data": workflow.nodes[i].data
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to update mode selection", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update mode selection"
        )


@router.get("/{workflow_id}/nodes/{node_id}/data")
async def get_node_data(
    workflow_id: str,
    node_id: str,
    current_user: User = Depends(get_current_active_user)
):
    """Get node data"""
    try:
        from bson import ObjectId
        try:
            workflow = await Workflow.find_one(
                Workflow.id == ObjectId(workflow_id),
                Workflow.user_id == str(current_user.id)
            )
        except Exception:
            workflow = None
        
        if not workflow:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Workflow not found"
            )
        
        # Find the node
        node = next((n for n in workflow.nodes if n.id == node_id), None)
        if not node:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Node not found"
            )
        
        return {
            "node_id": node_id,
            "node_type": node.type.value,
            "data": node.data
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to get node data", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get node data"
        )


@router.get("/node-types")
async def get_available_node_types():
    """Get available node types for canvas"""
    return {
        "node_types": [
            {
                "type": NodeType.START.value,
                "name": "Start",
                "description": "Workflow entry point",
                "inputs": 0,
                "outputs": 1,
                "configurable": False
            },
            {
                "type": NodeType.UPLOAD_IMAGE.value,
                "name": "Upload Image",
                "description": "Upload product image",
                "inputs": 1,
                "outputs": 1,
                "configurable": True
            },
            {
                "type": NodeType.PRODUCT_DETAILS.value,
                "name": "Product Details",
                "description": "Provide product information",
                "inputs": 1,
                "outputs": 1,
                "configurable": True
            },
            {
                "type": NodeType.MODE_SELECTION.value,
                "name": "Mode Selection",
                "description": "Select advertisement mode",
                "inputs": 1,
                "outputs": 1,
                "configurable": True
            },
            {
                "type": NodeType.ANALYSIS.value,
                "name": "AI Analysis",
                "description": "OpenAI image analysis",
                "inputs": 1,
                "outputs": 1,
                "configurable": False
            },
            {
                "type": NodeType.GENERATION.value,
                "name": "Image Generation",
                "description": "Gemini image generation",
                "inputs": 1,
                "outputs": 1,
                "configurable": False
            },
            {
                "type": NodeType.CONVERSION.value,
                "name": "Image Conversion",
                "description": "Convert base64 to files",
                "inputs": 1,
                "outputs": 1,
                "configurable": False
            },
            {
                "type": NodeType.STORAGE.value,
                "name": "Cloud Storage",
                "description": "Save to Google Drive",
                "inputs": 1,
                "outputs": 1,
                "configurable": False
            },
            {
                "type": NodeType.EDITING.value,
                "name": "Image Editing",
                "description": "Edit generated images",
                "inputs": 1,
                "outputs": 1,
                "configurable": True
            },
            {
                "type": NodeType.END.value,
                "name": "End",
                "description": "Workflow completion",
                "inputs": 1,
                "outputs": 0,
                "configurable": False
            }
        ]
    }
