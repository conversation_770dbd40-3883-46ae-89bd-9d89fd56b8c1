"""
Workflow management API routes
"""
from typing import List, Dict, Any
from fastapi import API<PERSON>outer, Depends, HTTPException, status
import structlog

from app.core.dependencies import get_current_active_user
from app.models.user import User
from app.models.workflow import (
    Workflow, 
    WorkflowCreate, 
    WorkflowUpdate, 
    WorkflowResponse,
    WorkflowExecution,
    WorkflowExecutionCreate,
    WorkflowExecutionResponse,
    NodeType
)
from app.services.workflow_service import workflow_service

logger = structlog.get_logger()

router = APIRouter()


@router.post("/", response_model=WorkflowResponse)
async def create_workflow(
    workflow_data: WorkflowCreate,
    current_user: User = Depends(get_current_active_user)
):
    """Create a new workflow"""
    try:
        workflow = Workflow(
            name=workflow_data.name,
            description=workflow_data.description,
            user_id=str(current_user.id),
            nodes=workflow_data.nodes,
            edges=workflow_data.edges
        )
        
        await workflow.save()
        
        logger.info(
            "Workflow created",
            workflow_id=str(workflow.id),
            user_id=str(current_user.id),
            name=workflow.name
        )
        
        return workflow.to_response()
        
    except Exception as e:
        logger.error("Failed to create workflow", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create workflow"
        )


@router.get("/", response_model=List[WorkflowResponse])
async def list_workflows(
    current_user: User = Depends(get_current_active_user)
):
    """List user's workflows"""
    try:
        workflows = await Workflow.find({"user_id": str(current_user.id)}).to_list()
        return [workflow.to_response() for workflow in workflows]
        
    except Exception as e:
        logger.error("Failed to list workflows", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to list workflows"
        )


@router.get("/{workflow_id}", response_model=WorkflowResponse)
async def get_workflow(
    workflow_id: str,
    current_user: User = Depends(get_current_active_user)
):
    """Get a specific workflow"""
    try:
        from bson import ObjectId
        try:
            workflow = await Workflow.find_one(
                Workflow.id == ObjectId(workflow_id),
                Workflow.user_id == str(current_user.id)
            )
        except Exception:
            workflow = None
        
        if not workflow:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Workflow not found"
            )
        
        return workflow.to_response()
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to get workflow", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get workflow"
        )


@router.put("/{workflow_id}", response_model=WorkflowResponse)
async def update_workflow(
    workflow_id: str,
    workflow_data: WorkflowUpdate,
    current_user: User = Depends(get_current_active_user)
):
    """Update a workflow (canvas changes)"""
    try:
        from bson import ObjectId
        try:
            workflow = await Workflow.find_one(
                Workflow.id == ObjectId(workflow_id),
                Workflow.user_id == str(current_user.id)
            )
        except Exception:
            workflow = None
        
        if not workflow:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Workflow not found"
            )
        
        # Update fields
        if workflow_data.name is not None:
            workflow.name = workflow_data.name
        if workflow_data.description is not None:
            workflow.description = workflow_data.description
        if workflow_data.nodes is not None:
            workflow.nodes = workflow_data.nodes
        if workflow_data.edges is not None:
            workflow.edges = workflow_data.edges
        
        await workflow.save()
        
        logger.info(
            "Workflow updated",
            workflow_id=workflow_id,
            user_id=str(current_user.id)
        )
        
        return workflow.to_response()
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to update workflow", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update workflow"
        )


@router.delete("/{workflow_id}")
async def delete_workflow(
    workflow_id: str,
    current_user: User = Depends(get_current_active_user)
):
    """Delete a workflow"""
    try:
        workflow = await Workflow.find_one(
            Workflow.id == workflow_id,
            Workflow.user_id == str(current_user.id)
        )
        
        if not workflow:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Workflow not found"
            )
        
        await workflow.delete()
        
        logger.info(
            "Workflow deleted",
            workflow_id=workflow_id,
            user_id=str(current_user.id)
        )
        
        return {"message": "Workflow deleted successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to delete workflow", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete workflow"
        )


@router.post("/{workflow_id}/validate")
async def validate_workflow(
    workflow_id: str,
    current_user: User = Depends(get_current_active_user)
):
    """Validate workflow before execution"""
    try:
        from bson import ObjectId
        try:
            workflow = await Workflow.find_one(
                Workflow.id == ObjectId(workflow_id),
                Workflow.user_id == str(current_user.id)
            )
        except Exception:
            workflow = None
        
        if not workflow:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Workflow not found"
            )
        
        # Validation logic
        errors = []
        warnings = []
        
        # Check if workflow has start and end nodes
        has_start = any(node.type == NodeType.START for node in workflow.nodes)
        has_end = any(node.type == NodeType.END for node in workflow.nodes)
        
        if not has_start:
            errors.append("Workflow must have a START node")
        if not has_end:
            errors.append("Workflow must have an END node")
        
        # Check if end node is connected
        end_nodes = [node for node in workflow.nodes if node.type == NodeType.END]
        if end_nodes:
            end_node_id = end_nodes[0].id
            is_end_connected = any(edge.target == end_node_id for edge in workflow.edges)
            if not is_end_connected:
                errors.append("END node must be connected to execute workflow")
        
        # Check for required nodes
        required_nodes = [NodeType.UPLOAD_IMAGE, NodeType.PRODUCT_DETAILS, NodeType.MODE_SELECTION]
        for required_type in required_nodes:
            if not any(node.type == required_type for node in workflow.nodes):
                warnings.append(f"Missing recommended node: {required_type.value}")
        
        return {
            "valid": len(errors) == 0,
            "errors": errors,
            "warnings": warnings,
            "can_execute": len(errors) == 0 and has_end and is_end_connected
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to validate workflow", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to validate workflow"
        )
