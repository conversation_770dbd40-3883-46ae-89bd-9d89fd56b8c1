"""
Workflow execution service using LangGraph
"""
from typing import Dict, Any, Optional, AsyncGenerator
import uuid
from datetime import datetime
import structlog

from app.workflows.photoshot_workflow import compile_photoshot_workflow, PhotoshotState
from app.models.workflow import WorkflowExecution, WorkflowStatus
from app.models.user import User

logger = structlog.get_logger()


class WorkflowExecutionService:
    """Service for executing LangGraph workflows"""
    
    def __init__(self):
        self._compiled_graph = None
    
    async def _get_compiled_graph(self):
        """Get or compile the workflow graph"""
        if not self._compiled_graph:
            self._compiled_graph = await compile_photoshot_workflow()
        return self._compiled_graph
    
    async def start_workflow_execution(
        self,
        workflow_id: str,
        user: User,
        input_data: Dict[str, Any]
    ) -> WorkflowExecution:
        """
        Start a new workflow execution
        """
        try:
            # Create execution record
            execution = WorkflowExecution(
                workflow_id=workflow_id,
                user_id=str(user.id),
                status=WorkflowStatus.PENDING,
                input_data=input_data,
                langgraph_thread_id=str(uuid.uuid4())
            )
            await execution.save()
            
            logger.info(
                "Workflow execution started",
                execution_id=str(execution.id),
                workflow_id=workflow_id,
                user_id=str(user.id),
                thread_id=execution.langgraph_thread_id
            )
            
            return execution
            
        except Exception as e:
            logger.error("Failed to start workflow execution", error=str(e))
            raise
    
    async def execute_workflow_stream(
        self,
        execution: WorkflowExecution,
        input_data: Dict[str, Any]
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """
        Execute workflow with streaming updates
        """
        try:
            # Get compiled graph
            graph = await self._get_compiled_graph()
            
            # Prepare initial state
            initial_state: PhotoshotState = {
                "execution_id": str(execution.id),
                "user_id": execution.user_id,
                "workflow_id": execution.workflow_id,
                "status": WorkflowStatus.RUNNING,
                "nodes": {},  # Node states will be populated during execution
                "current_node": None,
                "completed_nodes": [],
                "uploaded_image": input_data.get("uploaded_image"),
                "product_details": input_data.get("product_details"),
                "selected_mode": input_data.get("selected_mode"),
                "analysis_results": None,
                "generation_prompt": None,
                "generated_images": [],
                "converted_images": [],
                "saved_images": [],
                "edited_images": [],
                "error_message": None,
                "messages": []
            }
            
            # Configuration for LangGraph execution
            config = {
                "configurable": {
                    "thread_id": execution.langgraph_thread_id,
                    "checkpoint_ns": "ai_photoshot"
                }
            }
            
            # Update execution status
            execution.status = WorkflowStatus.RUNNING
            execution.started_at = datetime.utcnow()
            await execution.save()
            
            # Stream workflow execution
            async for chunk in graph.astream(
                initial_state,
                config=config,
                stream_mode="values"
            ):
                # Yield the current state
                yield {
                    "type": "state_update",
                    "execution_id": str(execution.id),
                    "current_node": chunk.get("current_node"),
                    "status": chunk.get("status"),
                    "data": chunk
                }
                
                # Update execution record with latest state
                execution.output_data = chunk
                await execution.save()
            
            # Mark as completed
            execution.status = WorkflowStatus.COMPLETED
            execution.completed_at = datetime.utcnow()
            await execution.save()
            
            yield {
                "type": "workflow_completed",
                "execution_id": str(execution.id),
                "status": "completed",
                "final_state": execution.output_data
            }
            
            logger.info(
                "Workflow execution completed",
                execution_id=str(execution.id),
                thread_id=execution.langgraph_thread_id
            )
            
        except Exception as e:
            # Mark as failed
            execution.status = WorkflowStatus.FAILED
            execution.error_message = str(e)
            execution.completed_at = datetime.utcnow()
            await execution.save()
            
            logger.error(
                "Workflow execution failed",
                execution_id=str(execution.id),
                error=str(e)
            )
            
            yield {
                "type": "workflow_failed",
                "execution_id": str(execution.id),
                "status": "failed",
                "error": str(e)
            }
    
    async def get_workflow_state(
        self,
        execution: WorkflowExecution
    ) -> Optional[Dict[str, Any]]:
        """
        Get current workflow state from checkpoint
        """
        try:
            graph = await self._get_compiled_graph()
            
            config = {
                "configurable": {
                    "thread_id": execution.langgraph_thread_id,
                    "checkpoint_ns": "ai_photoshot"
                }
            }
            
            # Get current state from checkpointer
            state_snapshot = graph.get_state(config)
            
            if state_snapshot:
                return {
                    "execution_id": str(execution.id),
                    "thread_id": execution.langgraph_thread_id,
                    "checkpoint_id": state_snapshot.config.get("configurable", {}).get("checkpoint_id"),
                    "current_state": state_snapshot.values,
                    "next_nodes": state_snapshot.next,
                    "created_at": state_snapshot.created_at
                }
            
            return None
            
        except Exception as e:
            logger.error(
                "Failed to get workflow state",
                execution_id=str(execution.id),
                error=str(e)
            )
            return None
    
    async def resume_workflow(
        self,
        execution: WorkflowExecution,
        checkpoint_id: Optional[str] = None
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """
        Resume workflow execution from a checkpoint
        """
        try:
            graph = await self._get_compiled_graph()
            
            config = {
                "configurable": {
                    "thread_id": execution.langgraph_thread_id,
                    "checkpoint_ns": "ai_photoshot"
                }
            }
            
            if checkpoint_id:
                config["configurable"]["checkpoint_id"] = checkpoint_id
            
            # Resume from checkpoint
            async for chunk in graph.astream(
                None,  # No new input, resume from checkpoint
                config=config,
                stream_mode="values"
            ):
                yield {
                    "type": "state_update",
                    "execution_id": str(execution.id),
                    "current_node": chunk.get("current_node"),
                    "status": chunk.get("status"),
                    "data": chunk
                }
                
                # Update execution record
                execution.output_data = chunk
                await execution.save()
            
            logger.info(
                "Workflow resumed successfully",
                execution_id=str(execution.id),
                checkpoint_id=checkpoint_id
            )
            
        except Exception as e:
            logger.error(
                "Failed to resume workflow",
                execution_id=str(execution.id),
                error=str(e)
            )
            raise


# Global service instance
workflow_service = WorkflowExecutionService()
