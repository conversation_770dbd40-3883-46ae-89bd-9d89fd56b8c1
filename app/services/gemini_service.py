"""
Google Gemini service for image generation
"""
from typing import Dict, Any, List
import base64
import structlog
import google.generativeai as genai
from google.generativeai.types import HarmCategory, HarmBlockThreshold

from app.core.config import settings

logger = structlog.get_logger()


class GeminiService:
    """Service for Google Gemini API interactions"""
    
    def __init__(self):
        genai.configure(api_key=settings.google_api_key)
        self.model = genai.GenerativeModel(settings.gemini_model)
    
    async def generate_product_images(
        self,
        analysis_results: Dict[str, Any],
        product_details: Dict[str, Any],
        selected_mode: str,
        num_variants: int = 5
    ) -> List[Dict[str, Any]]:
        """
        Generate product advertisement images using Gemini
        """
        try:
            # Create generation prompts
            prompts = self._create_generation_prompts(
                analysis_results, 
                product_details, 
                selected_mode, 
                num_variants
            )
            
            generated_images = []
            
            for i, prompt in enumerate(prompts):
                try:
                    logger.info(f"Generating image variant {i+1}/{len(prompts)}")
                    
                    # Generate image with Gemini
                    response = self.model.generate_content(
                        prompt,
                        safety_settings={
                            HarmCategory.HARM_CATEGORY_HATE_SPEECH: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
                            HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
                            HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
                            HarmCategory.HARM_CATEGORY_HARASSMENT: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
                        }
                    )
                    
                    # Extract image data (assuming Gemini returns base64 encoded images)
                    if response.parts:
                        for part in response.parts:
                            if hasattr(part, 'inline_data'):
                                image_data = {
                                    "variant_id": f"variant_{i+1}",
                                    "base64_data": part.inline_data.data,
                                    "mime_type": part.inline_data.mime_type,
                                    "prompt": prompt,
                                    "generation_metadata": {
                                        "model": settings.gemini_model,
                                        "variant_number": i + 1,
                                        "total_variants": len(prompts)
                                    }
                                }
                                generated_images.append(image_data)
                    
                except Exception as e:
                    logger.error(f"Failed to generate image variant {i+1}", error=str(e))
                    # Continue with other variants
                    continue
            
            if not generated_images:
                raise Exception("No images were generated successfully")
            
            logger.info(f"Successfully generated {len(generated_images)} image variants")
            return generated_images
            
        except Exception as e:
            logger.error("Failed to generate images", error=str(e))
            raise
    
    def _create_generation_prompts(
        self,
        analysis_results: Dict[str, Any],
        product_details: Dict[str, Any],
        selected_mode: str,
        num_variants: int
    ) -> List[str]:
        """Create image generation prompts for different variants"""
        
        base_prompt = self._create_base_prompt(analysis_results, product_details, selected_mode)
        
        # Create variations for different styles/approaches
        variant_styles = [
            "professional studio photography style",
            "lifestyle photography with natural lighting",
            "minimalist modern aesthetic",
            "vibrant and colorful commercial style",
            "elegant luxury brand presentation"
        ]
        
        prompts = []
        for i in range(min(num_variants, len(variant_styles))):
            style = variant_styles[i]
            variant_prompt = f"{base_prompt}\n\nStyle: {style}\n\nGenerate a high-quality product advertisement image."
            prompts.append(variant_prompt)
        
        # If we need more variants than styles, create additional variations
        if num_variants > len(variant_styles):
            additional_modifiers = [
                "with dramatic lighting",
                "in a contemporary setting",
                "with soft, diffused lighting",
                "in an outdoor environment",
                "with artistic composition"
            ]
            
            for i in range(num_variants - len(variant_styles)):
                modifier = additional_modifiers[i % len(additional_modifiers)]
                variant_prompt = f"{base_prompt}\n\nVariation: {modifier}\n\nGenerate a high-quality product advertisement image."
                prompts.append(variant_prompt)
        
        return prompts
    
    def _create_base_prompt(
        self,
        analysis_results: Dict[str, Any],
        product_details: Dict[str, Any],
        selected_mode: str
    ) -> str:
        """Create base prompt for image generation"""
        
        product_name = product_details.get('name', 'Product')
        product_description = product_details.get('description', '')
        product_purpose = product_details.get('purpose', '')
        
        # Extract key elements from analysis
        visual_elements = analysis_results.get('visual_elements', {})
        keywords = analysis_results.get('generation_keywords', [])
        recommendations = analysis_results.get('recommendations', [])
        
        # Build the prompt
        prompt_parts = [
            f"Create a professional product advertisement image for: {product_name}",
            f"Product description: {product_description}",
            f"Product purpose: {product_purpose}"
        ]
        
        # Add mode-specific instructions
        if selected_mode == "with_model":
            prompt_parts.append(
                "Include an AI-generated human model interacting with or showcasing the product. "
                "The model should be diverse, professional, and appropriate for the product type. "
                "Focus on natural, authentic interaction with the product."
            )
        else:
            prompt_parts.append(
                "Showcase the product as the main focus without human models. "
                "Emphasize the product's features, design, and quality through composition and lighting."
            )
        
        # Add visual elements
        if visual_elements:
            background = visual_elements.get('background', 'clean, professional background')
            lighting = visual_elements.get('lighting', 'professional lighting')
            
            prompt_parts.extend([
                f"Background: {background}",
                f"Lighting: {lighting}"
            ])
        
        # Add keywords
        if keywords:
            keyword_string = ", ".join(keywords[:5])  # Limit to 5 keywords
            prompt_parts.append(f"Style keywords: {keyword_string}")
        
        # Add quality requirements
        prompt_parts.extend([
            "Requirements:",
            "- High resolution, commercial quality",
            "- Professional photography aesthetic",
            "- Sharp focus on the product",
            "- Appealing composition and framing",
            "- Suitable for marketing and advertising use",
            "- Clean, uncluttered presentation"
        ])
        
        return "\n".join(prompt_parts)
    
    def encode_image_to_base64(self, image_path: str) -> str:
        """Encode image file to base64 string"""
        try:
            with open(image_path, "rb") as image_file:
                return base64.b64encode(image_file.read()).decode('utf-8')
        except Exception as e:
            logger.error("Failed to encode image to base64", error=str(e))
            raise
    
    def decode_base64_to_image(self, base64_data: str, output_path: str) -> None:
        """Decode base64 string to image file"""
        try:
            image_data = base64.b64decode(base64_data)
            with open(output_path, "wb") as image_file:
                image_file.write(image_data)
        except Exception as e:
            logger.error("Failed to decode base64 to image", error=str(e))
            raise


# Global service instance
gemini_service = GeminiService()
