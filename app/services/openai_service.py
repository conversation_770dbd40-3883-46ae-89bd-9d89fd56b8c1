"""
OpenAI service for image analysis
"""
from typing import Dict, Any, Optional
import base64
import structlog
from openai import AsyncOpenA<PERSON>

from app.core.config import settings

logger = structlog.get_logger()


class OpenAIService:
    """Service for OpenAI API interactions"""
    
    def __init__(self):
        self.client = AsyncOpenAI(api_key=settings.openai_api_key)
    
    async def analyze_product_image(
        self,
        image_path: str,
        product_details: Dict[str, Any],
        selected_mode: str
    ) -> Dict[str, Any]:
        """
        Analyze product image using OpenAI Vision API
        """
        try:
            # Read and encode image
            with open(image_path, "rb") as image_file:
                image_data = base64.b64encode(image_file.read()).decode('utf-8')
            
            # Prepare analysis prompt
            prompt = self._create_analysis_prompt(product_details, selected_mode)
            
            # Call OpenAI Vision API
            response = await self.client.chat.completions.create(
                model=settings.openai_model,
                messages=[
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "text",
                                "text": prompt
                            },
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/jpeg;base64,{image_data}"
                                }
                            }
                        ]
                    }
                ],
                max_tokens=1000
            )
            
            analysis_text = response.choices[0].message.content
            
            # Parse and structure the analysis
            analysis_results = self._parse_analysis_response(analysis_text, product_details, selected_mode)
            
            logger.info("Image analysis completed successfully")
            return analysis_results
            
        except Exception as e:
            logger.error("Failed to analyze image", error=str(e))
            raise
    
    def _create_analysis_prompt(self, product_details: Dict[str, Any], selected_mode: str) -> str:
        """Create analysis prompt for OpenAI"""
        
        mode_context = ""
        if selected_mode == "with_model":
            mode_context = "The final advertisement will feature an AI-generated human model using/wearing/holding this product."
        else:
            mode_context = "The final advertisement will showcase the product as-is without human models."
        
        prompt = f"""
        You are an expert product photographer and marketing analyst. Analyze this product image for creating an AI-generated advertisement.

        Product Information:
        - Name: {product_details.get('name', 'Unknown')}
        - Description: {product_details.get('description', 'No description')}
        - Purpose: {product_details.get('purpose', 'General use')}
        - Additional Context: {product_details.get('additional_context', 'None')}

        Advertisement Mode: {mode_context}

        Please analyze the image and provide:

        1. PRODUCT IDENTIFICATION:
        - What type of product is this?
        - Key visual features and characteristics
        - Product category and style

        2. VISUAL ANALYSIS:
        - Current lighting and composition
        - Background and setting
        - Product positioning and angles
        - Color scheme and aesthetics

        3. MARKETING POTENTIAL:
        - Target audience suggestions
        - Key selling points visible in the image
        - Emotional appeal factors
        - Brand positioning opportunities

        4. ADVERTISEMENT RECOMMENDATIONS:
        - Optimal background/setting for the ad
        - Lighting and mood suggestions
        - Composition improvements
        - Style and aesthetic direction

        5. GENERATION PROMPT ELEMENTS:
        - Specific visual elements to emphasize
        - Atmosphere and mood keywords
        - Technical photography terms
        - Style descriptors

        Provide your analysis in a structured format that will help generate compelling advertisement images.
        """
        
        return prompt
    
    def _parse_analysis_response(
        self, 
        analysis_text: str, 
        product_details: Dict[str, Any], 
        selected_mode: str
    ) -> Dict[str, Any]:
        """Parse OpenAI analysis response into structured data"""
        
        # Extract key information from the analysis
        # This is a simplified parser - in production, you might use more sophisticated NLP
        
        analysis_results = {
            "raw_analysis": analysis_text,
            "product_details": product_details,
            "selected_mode": selected_mode,
            "extracted_features": self._extract_features(analysis_text),
            "recommendations": self._extract_recommendations(analysis_text),
            "generation_keywords": self._extract_keywords(analysis_text),
            "target_audience": self._extract_target_audience(analysis_text),
            "visual_elements": self._extract_visual_elements(analysis_text)
        }
        
        return analysis_results
    
    def _extract_features(self, text: str) -> list:
        """Extract product features from analysis"""
        # Simple keyword extraction - can be improved with NLP
        features = []
        
        feature_indicators = [
            "feature", "characteristic", "quality", "material", 
            "design", "style", "color", "texture", "size"
        ]
        
        lines = text.lower().split('\n')
        for line in lines:
            for indicator in feature_indicators:
                if indicator in line and len(line.strip()) > 10:
                    features.append(line.strip())
                    break
        
        return features[:5]  # Limit to top 5 features
    
    def _extract_recommendations(self, text: str) -> list:
        """Extract recommendations from analysis"""
        recommendations = []
        
        rec_indicators = [
            "recommend", "suggest", "should", "could", "optimal",
            "best", "improve", "enhance", "consider"
        ]
        
        lines = text.split('\n')
        for line in lines:
            for indicator in rec_indicators:
                if indicator.lower() in line.lower() and len(line.strip()) > 15:
                    recommendations.append(line.strip())
                    break
        
        return recommendations[:5]  # Limit to top 5 recommendations
    
    def _extract_keywords(self, text: str) -> list:
        """Extract generation keywords from analysis"""
        keywords = []
        
        # Look for descriptive words and phrases
        keyword_patterns = [
            "lighting", "background", "mood", "atmosphere", "style",
            "elegant", "modern", "vintage", "professional", "casual",
            "bright", "dark", "colorful", "minimalist", "luxurious"
        ]
        
        text_lower = text.lower()
        for pattern in keyword_patterns:
            if pattern in text_lower:
                keywords.append(pattern)
        
        return list(set(keywords))  # Remove duplicates
    
    def _extract_target_audience(self, text: str) -> str:
        """Extract target audience from analysis"""
        audience_indicators = [
            "target", "audience", "customer", "user", "buyer",
            "demographic", "market", "consumer"
        ]
        
        lines = text.split('\n')
        for line in lines:
            for indicator in audience_indicators:
                if indicator.lower() in line.lower() and len(line.strip()) > 10:
                    return line.strip()
        
        return "General consumers"
    
    def _extract_visual_elements(self, text: str) -> Dict[str, str]:
        """Extract visual elements for image generation"""
        elements = {
            "background": "neutral background",
            "lighting": "professional lighting",
            "composition": "centered composition",
            "style": "modern style"
        }
        
        text_lower = text.lower()
        
        # Extract background suggestions
        if "background" in text_lower:
            for line in text.split('\n'):
                if "background" in line.lower() and len(line.strip()) > 10:
                    elements["background"] = line.strip()
                    break
        
        # Extract lighting suggestions
        if "lighting" in text_lower:
            for line in text.split('\n'):
                if "lighting" in line.lower() and len(line.strip()) > 10:
                    elements["lighting"] = line.strip()
                    break
        
        return elements


# Global service instance
openai_service = OpenAIService()
