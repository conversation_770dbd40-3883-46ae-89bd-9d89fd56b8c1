"""
FastAPI main application
"""
import structlog
from contextlib import asynccontextmanager
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse

from app.core.config import settings
from app.core.database import connect_to_mongo, close_mongo_connection, ping_database


# Configure structured logging
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger()


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan events"""
    # Startup
    logger.info("Starting AI Photoshot application")
    try:
        await connect_to_mongo()
        logger.info("Application startup completed")
    except Exception as e:
        logger.error("Failed to start application", error=str(e))
        raise
    
    yield
    
    # Shutdown
    logger.info("Shutting down AI Photoshot application")
    await close_mongo_connection()
    logger.info("Application shutdown completed")


# Create FastAPI application
app = FastAPI(
    title=settings.app_name,
    version=settings.app_version,
    description="AI-powered photoshot application with node-based workflow",
    lifespan=lifespan,
    debug=settings.debug
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": f"Welcome to {settings.app_name}",
        "version": settings.app_version,
        "status": "running"
    }


@app.get("/health")
async def health_check():
    """Health check endpoint"""
    try:
        db_status = await ping_database()
        return {
            "status": "healthy" if db_status else "unhealthy",
            "database": "connected" if db_status else "disconnected",
            "version": settings.app_version
        }
    except Exception as e:
        logger.error("Health check failed", error=str(e))
        raise HTTPException(status_code=503, detail="Service unavailable")


# Global exception handler
@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """Global exception handler"""
    logger.error(
        "Unhandled exception",
        path=request.url.path,
        method=request.method,
        error=str(exc),
        exc_info=True
    )
    return JSONResponse(
        status_code=500,
        content={"detail": "Internal server error"}
    )


# Include API routers
from app.api import auth, workflow, execution, nodes
app.include_router(auth.router, prefix=f"{settings.api_v1_prefix}/auth", tags=["auth"])
app.include_router(workflow.router, prefix=f"{settings.api_v1_prefix}/workflows", tags=["workflows"])
app.include_router(execution.router, prefix=f"{settings.api_v1_prefix}/workflows", tags=["execution"])
app.include_router(nodes.router, prefix=f"{settings.api_v1_prefix}/workflows", tags=["nodes"])

# Public endpoints (no auth required)
@app.get(f"{settings.api_v1_prefix}/node-types")
async def get_node_types():
    """Get available node types for canvas (public endpoint)"""
    from app.models.workflow import NodeType
    return {
        "node_types": [
            {
                "type": NodeType.START.value,
                "name": "Start",
                "description": "Workflow entry point",
                "inputs": 0,
                "outputs": 1,
                "configurable": False
            },
            {
                "type": NodeType.UPLOAD_IMAGE.value,
                "name": "Upload Image",
                "description": "Upload product image",
                "inputs": 1,
                "outputs": 1,
                "configurable": True
            },
            {
                "type": NodeType.PRODUCT_DETAILS.value,
                "name": "Product Details",
                "description": "Provide product information",
                "inputs": 1,
                "outputs": 1,
                "configurable": True
            },
            {
                "type": NodeType.MODE_SELECTION.value,
                "name": "Mode Selection",
                "description": "Select advertisement mode",
                "inputs": 1,
                "outputs": 1,
                "configurable": True
            },
            {
                "type": NodeType.ANALYSIS.value,
                "name": "AI Analysis",
                "description": "OpenAI image analysis",
                "inputs": 1,
                "outputs": 1,
                "configurable": False
            },
            {
                "type": NodeType.GENERATION.value,
                "name": "Image Generation",
                "description": "Gemini image generation",
                "inputs": 1,
                "outputs": 1,
                "configurable": False
            },
            {
                "type": NodeType.CONVERSION.value,
                "name": "Image Conversion",
                "description": "Convert base64 to files",
                "inputs": 1,
                "outputs": 1,
                "configurable": False
            },
            {
                "type": NodeType.STORAGE.value,
                "name": "Cloud Storage",
                "description": "Save to Google Drive",
                "inputs": 1,
                "outputs": 1,
                "configurable": False
            },
            {
                "type": NodeType.EDITING.value,
                "name": "Image Editing",
                "description": "Edit generated images",
                "inputs": 1,
                "outputs": 1,
                "configurable": True
            },
            {
                "type": NodeType.END.value,
                "name": "End",
                "description": "Workflow completion",
                "inputs": 1,
                "outputs": 0,
                "configurable": False
            }
        ]
    }


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.debug,
        log_level="info"
    )
