"""
Application configuration settings
"""
from typing import Optional
from pydantic_settings import BaseSettings
from pydantic import Field


class Settings(BaseSettings):
    """Application settings"""
    
    # Application
    app_name: str = "AI Photoshot"
    app_version: str = "0.1.0"
    debug: bool = False
    
    # API
    api_v1_prefix: str = "/api/v1"
    
    # Database
    mongodb_url: str = Field(default="mongodb://localhost:27017", env="MONGODB_URL")
    database_name: str = Field(default="ai_photoshot", env="DATABASE_NAME")
    
    # Authentication
    secret_key: str = Field(env="SECRET_KEY")
    algorithm: str = "HS256"
    access_token_expire_minutes: int = 30
    
    # OpenAI
    openai_api_key: str = Field(env="OPENAI_API_KEY")
    openai_model: str = "gpt-4-vision-preview"
    
    # Google Gemini
    google_api_key: str = Field(env="GOOGLE_API_KEY")
    gemini_model: str = "gemini-2.0-flash-exp"
    
    # Google Drive
    google_drive_credentials_file: Optional[str] = Field(default=None, env="GOOGLE_DRIVE_CREDENTIALS_FILE")
    
    # File Upload
    max_file_size: int = 10 * 1024 * 1024  # 10MB
    allowed_image_types: list[str] = ["image/jpeg", "image/png", "image/webp"]
    upload_dir: str = "uploads"
    
    # LangGraph
    langgraph_checkpoint_namespace: str = "ai_photoshot_workflows"
    
    class Config:
        env_file = ".env"
        case_sensitive = False


# Global settings instance
settings = Settings()
