"""
MongoDB database connection and initialization
"""
import asyncio
from typing import Optional
from motor.motor_asyncio import AsyncIOMotorClient, AsyncIOMotorDatabase
from beanie import init_beanie
import structlog

from app.core.config import settings
from app.models.user import User
from app.models.workflow import Workflow, WorkflowExecution
from app.models.node import NodeExecution
from app.models.image import ImageData

logger = structlog.get_logger()


class Database:
    client: Optional[AsyncIOMotorClient] = None
    database: Optional[AsyncIOMotorDatabase] = None


db = Database()


async def connect_to_mongo():
    """Create database connection"""
    try:
        db.client = AsyncIOMotorClient(settings.mongodb_url)
        db.database = db.client[settings.database_name]
        
        # Test connection
        await db.client.admin.command('ping')
        logger.info("Connected to MongoDB", database=settings.database_name)
        
        # Initialize Beanie with document models
        await init_beanie(
            database=db.database,
            document_models=[
                User,
                Workflow,
                WorkflowExecution,
                NodeExecution,
                ImageData
            ]
        )
        logger.info("Beanie initialized with document models")
        
    except Exception as e:
        logger.error("Failed to connect to MongoDB", error=str(e))
        raise


async def close_mongo_connection():
    """Close database connection"""
    if db.client:
        db.client.close()
        logger.info("Disconnected from MongoDB")

    # Also close checkpointer connection
    from app.workflows.checkpoints.mongodb_saver import checkpointer_manager
    await checkpointer_manager.close()


async def get_database() -> AsyncIOMotorDatabase:
    """Get database instance"""
    if not db.database:
        await connect_to_mongo()
    return db.database


# Health check function
async def ping_database() -> bool:
    """Check if database is accessible"""
    try:
        if not db.client:
            return False
        await db.client.admin.command('ping')
        return True
    except Exception:
        return False
