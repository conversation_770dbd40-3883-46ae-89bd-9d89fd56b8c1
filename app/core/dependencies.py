"""
FastAPI dependencies
"""
from typing import Optional
from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from motor.motor_asyncio import AsyncIOMotorDatabase

from app.core.database import get_database
from app.core.security import verify_token, credentials_exception
from app.models.user import User


# Security scheme
security = HTTPBearer()


async def get_db() -> AsyncIOMotorDatabase:
    """Database dependency"""
    return await get_database()


async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security)
) -> User:
    """Get current authenticated user"""
    token = credentials.credentials
    token_data = verify_token(token, credentials_exception)
    
    user = await User.find_one(User.username == token_data.username)
    if user is None:
        raise credentials_exception
    return user


async def get_current_active_user(
    current_user: User = Depends(get_current_user)
) -> User:
    """Get current active user"""
    if not current_user.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Inactive user"
        )
    return current_user


async def get_optional_user(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security)
) -> Optional[User]:
    """Get user if authenticated, None otherwise"""
    if not credentials:
        return None
    
    try:
        token = credentials.credentials
        token_data = verify_token(token, credentials_exception)
        user = await User.find_one(User.username == token_data.username)
        return user if user and user.is_active else None
    except HTTPException:
        return None
