"""
Main AI Photoshot workflow using LangGraph
"""
from typing import Dict, Any, List, Optional
from typing_extensions import TypedDict
from datetime import datetime
import uuid
import structlog

from langgraph.graph import StateGraph, START, END
from langgraph.graph.message import add_messages
from langchain_core.messages import BaseMessage

from app.workflows.checkpoints.mongodb_saver import checkpointer_manager
from app.models.workflow import NodeType, WorkflowStatus
from app.models.image import ProductDetails, ImageSource

logger = structlog.get_logger()


class PhotoshotState(TypedDict):
    """State for the AI Photoshot workflow"""
    # Workflow metadata
    execution_id: str
    user_id: str
    workflow_id: str
    status: WorkflowStatus

    # Node states - each node updates its own state
    nodes: Dict[str, Dict[str, Any]]  # node_id -> node_state

    # Flow control
    current_node: Optional[str]
    completed_nodes: List[str]

    # Input data (from canvas nodes)
    uploaded_image: Optional[Dict[str, Any]]  # From upload_image node
    product_details: Optional[ProductDetails]  # From product_details node
    selected_mode: Optional[str]  # From mode_selection node

    # Processing data (updated by workflow execution)
    analysis_results: Optional[Dict[str, Any]]  # From analysis node
    generation_prompt: Optional[str]  # Generated prompt for image generation
    generated_images: List[Dict[str, Any]]  # From generation node
    converted_images: List[Dict[str, Any]]  # From conversion node

    # Output data
    saved_images: List[Dict[str, Any]]  # From storage node
    edited_images: List[Dict[str, Any]]  # From editing node

    # Error handling
    error_message: Optional[str]

    # Messages for logging/debugging
    messages: List[BaseMessage]


def create_photoshot_workflow() -> StateGraph:
    """
    Create the main AI Photoshot workflow graph
    """
    
    # Define the workflow graph
    workflow = StateGraph(PhotoshotState)
    
    # Add workflow nodes (will be implemented in separate files)
    workflow.add_node("start", start_node)
    workflow.add_node("upload_image", upload_image_node)
    workflow.add_node("product_details", product_details_node)
    workflow.add_node("mode_selection", mode_selection_node)
    workflow.add_node("analysis", analysis_node)
    workflow.add_node("generation", generation_node)
    workflow.add_node("conversion", conversion_node)
    workflow.add_node("storage", storage_node)
    workflow.add_node("editing", editing_node)
    workflow.add_node("end", end_node)
    
    # Define workflow edges
    workflow.add_edge(START, "start")
    workflow.add_edge("start", "upload_image")
    workflow.add_edge("upload_image", "product_details")
    workflow.add_edge("product_details", "mode_selection")
    workflow.add_edge("mode_selection", "analysis")
    workflow.add_edge("analysis", "generation")
    workflow.add_edge("generation", "conversion")
    workflow.add_edge("conversion", "storage")
    workflow.add_edge("storage", "editing")
    workflow.add_edge("editing", "end")
    workflow.add_edge("end", END)
    
    return workflow


async def compile_photoshot_workflow() -> StateGraph:
    """
    Compile the photoshot workflow with MongoDB checkpointing
    """
    try:
        # Get the workflow graph
        workflow = create_photoshot_workflow()
        
        # Get MongoDB checkpointer
        checkpointer = await checkpointer_manager.get_checkpointer()
        
        # Compile with checkpointer
        compiled_graph = workflow.compile(checkpointer=checkpointer)
        
        logger.info("Photoshot workflow compiled successfully with MongoDB checkpointing")
        return compiled_graph
        
    except Exception as e:
        logger.error("Failed to compile photoshot workflow", error=str(e))
        raise


# Placeholder node functions (will be implemented in separate files)
def start_node(state: PhotoshotState) -> PhotoshotState:
    """Start node - initialize workflow"""
    logger.info("Starting photoshot workflow", execution_id=state.get("execution_id"))
    return {
        **state,
        "status": WorkflowStatus.RUNNING,
        "current_node": "start",
        "messages": add_messages(
            state.get("messages", []),
            [{"role": "system", "content": "Photoshot workflow started"}]
        )
    }


def upload_image_node(state: PhotoshotState) -> PhotoshotState:
    """Upload image node - handle image upload"""
    logger.info("Processing image upload", execution_id=state.get("execution_id"))
    return {
        **state,
        "current_node": "upload_image",
        "messages": add_messages(
            state.get("messages", []),
            [{"role": "system", "content": "Image upload processed"}]
        )
    }


def product_details_node(state: PhotoshotState) -> PhotoshotState:
    """Product details node - collect product information"""
    logger.info("Processing product details", execution_id=state.get("execution_id"))
    return {
        **state,
        "current_node": "product_details",
        "messages": add_messages(
            state.get("messages", []),
            [{"role": "system", "content": "Product details collected"}]
        )
    }


def mode_selection_node(state: PhotoshotState) -> PhotoshotState:
    """Mode selection node - select advertisement mode"""
    logger.info("Processing mode selection", execution_id=state.get("execution_id"))
    return {
        **state,
        "current_node": "mode_selection",
        "messages": add_messages(
            state.get("messages", []),
            [{"role": "system", "content": "Advertisement mode selected"}]
        )
    }


def analysis_node(state: PhotoshotState) -> PhotoshotState:
    """Analysis node - OpenAI image analysis"""
    logger.info("Processing image analysis", execution_id=state.get("execution_id"))
    return {
        **state,
        "current_node": "analysis",
        "messages": add_messages(
            state.get("messages", []),
            [{"role": "system", "content": "Image analysis completed"}]
        )
    }


def generation_node(state: PhotoshotState) -> PhotoshotState:
    """Generation node - Gemini image generation"""
    logger.info("Processing image generation", execution_id=state.get("execution_id"))
    return {
        **state,
        "current_node": "generation",
        "messages": add_messages(
            state.get("messages", []),
            [{"role": "system", "content": "Images generated"}]
        )
    }


def conversion_node(state: PhotoshotState) -> PhotoshotState:
    """Conversion node - convert base64 to files"""
    logger.info("Processing image conversion", execution_id=state.get("execution_id"))
    return {
        **state,
        "current_node": "conversion",
        "messages": add_messages(
            state.get("messages", []),
            [{"role": "system", "content": "Images converted"}]
        )
    }


def storage_node(state: PhotoshotState) -> PhotoshotState:
    """Storage node - save to Google Drive"""
    logger.info("Processing image storage", execution_id=state.get("execution_id"))
    return {
        **state,
        "current_node": "storage",
        "messages": add_messages(
            state.get("messages", []),
            [{"role": "system", "content": "Images stored"}]
        )
    }


def editing_node(state: PhotoshotState) -> PhotoshotState:
    """Editing node - image editing capabilities"""
    logger.info("Processing image editing", execution_id=state.get("execution_id"))
    return {
        **state,
        "current_node": "editing",
        "messages": add_messages(
            state.get("messages", []),
            [{"role": "system", "content": "Image editing completed"}]
        )
    }


def end_node(state: PhotoshotState) -> PhotoshotState:
    """End node - finalize workflow"""
    logger.info("Completing photoshot workflow", execution_id=state.get("execution_id"))
    return {
        **state,
        "status": WorkflowStatus.COMPLETED,
        "current_node": "end",
        "messages": add_messages(
            state.get("messages", []),
            [{"role": "system", "content": "Photoshot workflow completed successfully"}]
        )
    }
