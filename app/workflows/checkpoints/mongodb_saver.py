"""
MongoDB checkpoint utilities for LangGraph
"""
from typing import Optional
import structlog
from langgraph.checkpoint.mongodb.aio import AsyncMongoDBSaver
from motor.motor_asyncio import AsyncIOMotorDatabase

from app.core.database import get_database
from app.core.config import settings

logger = structlog.get_logger()


async def get_mongodb_checkpointer() -> AsyncMongoDBSaver:
    """
    Get MongoDB checkpointer instance using LangGraph's built-in AsyncMongoDBSaver
    """
    try:
        # Use LangGraph's built-in MongoDB checkpointer
        checkpointer = AsyncMongoDBSaver.from_conn_string(
            settings.mongodb_url,
            db_name=settings.database_name
        )

        # Setup the checkpointer (creates necessary collections/indexes)
        await checkpointer.setup()

        logger.info(
            "MongoDB checkpointer initialized",
            database=settings.database_name,
            url=settings.mongodb_url.split('@')[-1] if '@' in settings.mongodb_url else settings.mongodb_url
        )

        return checkpointer

    except Exception as e:
        logger.error("Failed to initialize MongoDB checkpointer", error=str(e))
        raise


class CheckpointerManager:
    """
    Manager for MongoDB checkpointer lifecycle
    """

    def __init__(self):
        self._checkpointer: Optional[AsyncMongoDBSaver] = None

    async def get_checkpointer(self) -> AsyncMongoDBSaver:
        """Get or create checkpointer instance"""
        if not self._checkpointer:
            self._checkpointer = await get_mongodb_checkpointer()
        return self._checkpointer

    async def close(self):
        """Close checkpointer connection"""
        if self._checkpointer:
            # Note: AsyncMongoDBSaver doesn't have aclose method in newer versions
            # The connection is managed by the MongoDB driver
            self._checkpointer = None
            logger.info("MongoDB checkpointer closed")


# Global checkpointer manager instance
checkpointer_manager = CheckpointerManager()
