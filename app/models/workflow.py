"""
Workflow models
"""
from datetime import datetime
from typing import Dict, List, Optional, Any
from enum import Enum
from beanie import Document
from pydantic import BaseModel, Field
from pymongo import IndexModel


class WorkflowStatus(str, Enum):
    """Workflow execution status"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class NodeType(str, Enum):
    """Available node types"""
    START = "start"
    END = "end"
    UPLOAD_IMAGE = "upload_image"
    PRODUCT_DETAILS = "product_details"
    MODE_SELECTION = "mode_selection"
    ANALYSIS = "analysis"
    GENERATION = "generation"
    CONVERSION = "conversion"
    STORAGE = "storage"
    EDITING = "editing"


class WorkflowNode(BaseModel):
    """Workflow node definition"""
    id: str
    type: NodeType
    position: Dict[str, float]  # x, y coordinates
    data: Dict[str, Any] = Field(default_factory=dict)
    inputs: List[str] = Field(default_factory=list)  # Connected input node IDs
    outputs: List[str] = Field(default_factory=list)  # Connected output node IDs


class WorkflowEdge(BaseModel):
    """Workflow edge/connection"""
    id: str
    source: str  # Source node ID
    target: str  # Target node ID
    source_handle: Optional[str] = None
    target_handle: Optional[str] = None


class WorkflowCreate(BaseModel):
    """Workflow creation model"""
    name: str = Field(..., min_length=1, max_length=100)
    description: Optional[str] = None
    nodes: List[WorkflowNode] = Field(default_factory=list)
    edges: List[WorkflowEdge] = Field(default_factory=list)


class WorkflowUpdate(BaseModel):
    """Workflow update model"""
    name: Optional[str] = None
    description: Optional[str] = None
    nodes: Optional[List[WorkflowNode]] = None
    edges: Optional[List[WorkflowEdge]] = None


class WorkflowResponse(BaseModel):
    """Workflow response model"""
    id: str
    name: str
    description: Optional[str]
    user_id: str
    nodes: List[WorkflowNode]
    edges: List[WorkflowEdge]
    created_at: datetime
    updated_at: datetime


class Workflow(Document):
    """Workflow document model"""
    name: str = Field(..., min_length=1, max_length=100)
    description: Optional[str] = None
    user_id: str  # Reference to User
    nodes: List[WorkflowNode] = Field(default_factory=list)
    edges: List[WorkflowEdge] = Field(default_factory=list)
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    
    class Settings:
        name = "workflows"
        indexes = [
            IndexModel([("user_id", 1)]),
            IndexModel([("created_at", -1)]),
        ]
    
    def to_response(self) -> WorkflowResponse:
        """Convert to response model"""
        return WorkflowResponse(
            id=str(self.id),
            name=self.name,
            description=self.description,
            user_id=self.user_id,
            nodes=self.nodes,
            edges=self.edges,
            created_at=self.created_at,
            updated_at=self.updated_at
        )


class WorkflowExecutionCreate(BaseModel):
    """Workflow execution creation model"""
    workflow_id: str
    input_data: Dict[str, Any] = Field(default_factory=dict)


class WorkflowExecutionResponse(BaseModel):
    """Workflow execution response model"""
    id: str
    workflow_id: str
    user_id: str
    status: WorkflowStatus
    input_data: Dict[str, Any]
    output_data: Dict[str, Any]
    error_message: Optional[str]
    started_at: datetime
    completed_at: Optional[datetime]
    langgraph_thread_id: str


class WorkflowExecution(Document):
    """Workflow execution document model"""
    workflow_id: str  # Reference to Workflow
    user_id: str  # Reference to User
    status: WorkflowStatus = WorkflowStatus.PENDING
    input_data: Dict[str, Any] = Field(default_factory=dict)
    output_data: Dict[str, Any] = Field(default_factory=dict)
    error_message: Optional[str] = None
    started_at: datetime = Field(default_factory=datetime.utcnow)
    completed_at: Optional[datetime] = None
    langgraph_thread_id: str  # LangGraph thread ID for checkpointing
    
    class Settings:
        name = "workflow_executions"
        indexes = [
            IndexModel([("workflow_id", 1)]),
            IndexModel([("user_id", 1)]),
            IndexModel([("status", 1)]),
            IndexModel([("started_at", -1)]),
            IndexModel([("langgraph_thread_id", 1)], unique=True),
        ]
    
    def to_response(self) -> WorkflowExecutionResponse:
        """Convert to response model"""
        return WorkflowExecutionResponse(
            id=str(self.id),
            workflow_id=self.workflow_id,
            user_id=self.user_id,
            status=self.status,
            input_data=self.input_data,
            output_data=self.output_data,
            error_message=self.error_message,
            started_at=self.started_at,
            completed_at=self.completed_at,
            langgraph_thread_id=self.langgraph_thread_id
        )
