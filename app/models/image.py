"""
Image-related models
"""
from datetime import datetime
from typing import Optional, List
from enum import Enum
from beanie import Document
from pydantic import BaseModel, Field, HttpUrl
from pymongo import IndexModel


class ImageFormat(str, Enum):
    """Supported image formats"""
    JPEG = "jpeg"
    PNG = "png"
    WEBP = "webp"


class ImageSource(str, Enum):
    """Image source types"""
    UPLOAD = "upload"
    GENERATED = "generated"
    EDITED = "edited"


class ProductMode(str, Enum):
    """Product advertisement modes"""
    WITH_MODEL = "with_model"  # Product with AI human model
    PRODUCT_ONLY = "product_only"  # Product as is


class ImageMetadata(BaseModel):
    """Image metadata"""
    width: int
    height: int
    format: ImageFormat
    size_bytes: int
    mime_type: str


class ProductDetails(BaseModel):
    """Product details for image generation"""
    name: str = Field(..., min_length=1, max_length=200)
    description: str = Field(..., min_length=1, max_length=1000)
    purpose: str = Field(..., min_length=1, max_length=500)
    mode: ProductMode
    additional_context: Optional[str] = None


class ImageEditOperation(BaseModel):
    """Image editing operation"""
    operation: str  # zoom_in, zoom_out, crop, rotate, upscale
    parameters: dict = Field(default_factory=dict)


class ImageResponse(BaseModel):
    """Image response model"""
    id: str
    filename: str
    original_filename: Optional[str]
    source: ImageSource
    metadata: ImageMetadata
    file_path: Optional[str]
    url: Optional[HttpUrl]
    google_drive_id: Optional[str]
    execution_id: Optional[str]
    user_id: str
    created_at: datetime


class ImageData(Document):
    """Image data document model"""
    filename: str
    original_filename: Optional[str] = None
    source: ImageSource
    metadata: ImageMetadata
    file_path: Optional[str] = None  # Local file path
    base64_data: Optional[str] = None  # Base64 encoded image data
    google_drive_id: Optional[str] = None  # Google Drive file ID
    execution_id: Optional[str] = None  # Reference to WorkflowExecution
    user_id: str  # Reference to User
    created_at: datetime = Field(default_factory=datetime.utcnow)
    
    # Generation-specific fields
    generation_prompt: Optional[str] = None
    product_details: Optional[ProductDetails] = None
    
    # Editing history
    edit_operations: List[ImageEditOperation] = Field(default_factory=list)
    parent_image_id: Optional[str] = None  # Reference to original image if this is edited
    
    class Settings:
        name = "images"
        indexes = [
            IndexModel([("user_id", 1)]),
            IndexModel([("execution_id", 1)]),
            IndexModel([("source", 1)]),
            IndexModel([("created_at", -1)]),
            IndexModel([("google_drive_id", 1)]),
        ]
    
    def to_response(self) -> ImageResponse:
        """Convert to response model"""
        return ImageResponse(
            id=str(self.id),
            filename=self.filename,
            original_filename=self.original_filename,
            source=self.source,
            metadata=self.metadata,
            file_path=self.file_path,
            url=None,  # Will be set by the API
            google_drive_id=self.google_drive_id,
            execution_id=self.execution_id,
            user_id=self.user_id,
            created_at=self.created_at
        )
