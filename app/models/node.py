"""
Node execution models
"""
from datetime import datetime
from typing import Dict, Any, Optional
from enum import Enum
from beanie import Document
from pydantic import BaseModel, Field
from pymongo import IndexModel

from app.models.workflow import NodeType


class NodeStatus(str, Enum):
    """Node execution status"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    SKIPPED = "skipped"


class NodeExecutionResponse(BaseModel):
    """Node execution response model"""
    id: str
    execution_id: str
    node_id: str
    node_type: NodeType
    status: NodeStatus
    input_data: Dict[str, Any]
    output_data: Dict[str, Any]
    error_message: Optional[str]
    started_at: datetime
    completed_at: Optional[datetime]
    duration_seconds: Optional[float]


class NodeExecution(Document):
    """Node execution document model"""
    execution_id: str  # Reference to WorkflowExecution
    node_id: str  # Node ID from workflow definition
    node_type: NodeType
    status: NodeStatus = NodeStatus.PENDING
    input_data: Dict[str, Any] = Field(default_factory=dict)
    output_data: Dict[str, Any] = Field(default_factory=dict)
    error_message: Optional[str] = None
    started_at: datetime = Field(default_factory=datetime.utcnow)
    completed_at: Optional[datetime] = None
    duration_seconds: Optional[float] = None
    
    class Settings:
        name = "node_executions"
        indexes = [
            IndexModel([("execution_id", 1)]),
            IndexModel([("node_id", 1)]),
            IndexModel([("status", 1)]),
            IndexModel([("started_at", -1)]),
        ]
    
    def to_response(self) -> NodeExecutionResponse:
        """Convert to response model"""
        return NodeExecutionResponse(
            id=str(self.id),
            execution_id=self.execution_id,
            node_id=self.node_id,
            node_type=self.node_type,
            status=self.status,
            input_data=self.input_data,
            output_data=self.output_data,
            error_message=self.error_message,
            started_at=self.started_at,
            completed_at=self.completed_at,
            duration_seconds=self.duration_seconds
        )
