{".class": "MypyFile", "_fullname": "app.core.security", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "BaseModel": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "app.core.security.BaseModel", "name": "BaseModel", "type": {".class": "AnyType", "missing_import_name": "app.core.security.BaseModel", "source_any": null, "type_of_any": 3}}}, "CryptContext": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "app.core.security.CryptContext", "name": "CryptContext", "type": {".class": "AnyType", "missing_import_name": "app.core.security.CryptContext", "source_any": null, "type_of_any": 3}}}, "HTTPException": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "app.core.security.HTTPException", "name": "HTTPException", "type": {".class": "AnyType", "missing_import_name": "app.core.security.HTTPException", "source_any": null, "type_of_any": 3}}}, "JWTError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "app.core.security.JWTError", "name": "JWTError", "type": {".class": "AnyType", "missing_import_name": "app.core.security.JWTError", "source_any": null, "type_of_any": 3}}}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Token": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "app.core.security.Token", "name": "Token", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "app.core.security.Token", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "app.core.security", "mro": ["app.core.security.Token", "builtins.object"], "names": {".class": "SymbolTable", "access_token": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "app.core.security.Token.access_token", "name": "access_token", "type": "builtins.str"}}, "token_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "app.core.security.Token.token_type", "name": "token_type", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "app.core.security.Token.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "app.core.security.Token", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TokenData": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "app.core.security.TokenData", "name": "TokenData", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "app.core.security.TokenData", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "app.core.security", "mro": ["app.core.security.TokenData", "builtins.object"], "names": {".class": "SymbolTable", "username": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "app.core.security.TokenData.username", "name": "username", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "app.core.security.TokenData.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "app.core.security.TokenData", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.core.security.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.core.security.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.core.security.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.core.security.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.core.security.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.core.security.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "create_access_token": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["data", "expires_delta"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "app.core.security.create_access_token", "name": "create_access_token", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["data", "expires_delta"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "UnionType", "items": ["datetime.<PERSON><PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_access_token", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "credentials_exception": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "app.core.security.credentials_exception", "name": "credentials_exception", "type": {".class": "AnyType", "missing_import_name": "app.core.security.HTTPException", "source_any": {".class": "AnyType", "missing_import_name": "app.core.security.HTTPException", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime.datetime", "kind": "Gdef"}, "get_password_hash": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["password"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "app.core.security.get_password_hash", "name": "get_password_hash", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["password"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_password_hash", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "inactive_user_exception": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "app.core.security.inactive_user_exception", "name": "inactive_user_exception", "type": {".class": "AnyType", "missing_import_name": "app.core.security.HTTPException", "source_any": {".class": "AnyType", "missing_import_name": "app.core.security.HTTPException", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "jwt": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "app.core.security.jwt", "name": "jwt", "type": {".class": "AnyType", "missing_import_name": "app.core.security.jwt", "source_any": null, "type_of_any": 3}}}, "pwd_context": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "app.core.security.pwd_context", "name": "pwd_context", "type": {".class": "AnyType", "missing_import_name": "app.core.security.CryptContext", "source_any": {".class": "AnyType", "missing_import_name": "app.core.security.CryptContext", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "settings": {".class": "SymbolTableNode", "cross_ref": "app.core.config.settings", "kind": "Gdef"}, "status": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "app.core.security.status", "name": "status", "type": {".class": "AnyType", "missing_import_name": "app.core.security.status", "source_any": null, "type_of_any": 3}}}, "timedelta": {".class": "SymbolTableNode", "cross_ref": "datetime.<PERSON><PERSON><PERSON>", "kind": "Gdef"}, "verify_password": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["plain_password", "hashed_password"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "app.core.security.verify_password", "name": "verify_password", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["plain_password", "hashed_password"], "arg_types": ["builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "verify_password", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "verify_token": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["token", "credentials_exception"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "app.core.security.verify_token", "name": "verify_token", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["token", "credentials_exception"], "arg_types": ["builtins.str", {".class": "AnyType", "missing_import_name": "app.core.security.HTTPException", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "verify_token", "ret_type": "app.core.security.TokenData", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "path": "/Users/<USER>/Developers/Tinkering/ShoshinAI/app/core/security.py"}