{"data_mtime": 1751863342, "dep_lines": [4, 5, 1, 1, 1, 1, 6, 7, 8], "dep_prios": [5, 5, 5, 30, 30, 30, 5, 5, 5], "dependencies": ["datetime", "typing", "builtins", "_frozen_importlib", "abc", "types"], "hash": "0b38050ca3001fe08dc33f140e14f03dc9aeed37", "id": "app.models.user", "ignore_all": true, "interface_hash": "19e3d11894a081add1375f6a0f0622c938037b60", "mtime": 1751863289, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Developers/Tinkering/ShoshinAI/app/models/user.py", "plugin_data": null, "size": 1979, "suppressed": ["beanie", "pydantic", "pymongo"], "version_id": "1.15.0"}