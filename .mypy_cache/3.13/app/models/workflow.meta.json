{"data_mtime": 1751863342, "dep_lines": [4, 5, 6, 1, 1, 1, 1, 1, 1, 7, 8, 9], "dep_prios": [5, 5, 5, 5, 30, 30, 30, 30, 30, 5, 5, 5], "dependencies": ["datetime", "typing", "enum", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "types"], "hash": "cd06f3cec7128490789607effa55b998bb3032d0", "id": "app.models.workflow", "ignore_all": true, "interface_hash": "440e9b73ef06320361f8d7f18ad4163b11cd00bf", "mtime": 1751863289, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Developers/Tinkering/ShoshinAI/app/models/workflow.py", "plugin_data": null, "size": 5122, "suppressed": ["beanie", "pydantic", "pymongo"], "version_id": "1.15.0"}