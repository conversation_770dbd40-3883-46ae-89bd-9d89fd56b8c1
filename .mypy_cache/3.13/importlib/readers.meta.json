{"data_mtime": 1751863340, "dep_lines": [15, 9, 15, 5, 6, 7, 8, 10, 11, 12, 15, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 20, 10, 10, 10, 5, 5, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["importlib.resources.abc", "collections.abc", "importlib.resources", "pathlib", "sys", "zipfile", "_typeshed", "io", "typing", "typing_extensions", "importlib", "builtins", "_frozen_importlib", "_io", "abc", "importlib.abc", "os", "types", "zipfile._path"], "hash": "5527c48630fb2b623985ff211aabecfaba9fa570", "id": "importlib.readers", "ignore_all": true, "interface_hash": "f9561723ef5da0bb589ebc93fc95177131419a38", "mtime": 1750496964, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/.vscode/extensions/ms-python.mypy-type-checker-2025.2.0/bundled/libs/mypy/typeshed/stdlib/importlib/readers.pyi", "plugin_data": null, "size": 2584, "suppressed": [], "version_id": "1.15.0"}