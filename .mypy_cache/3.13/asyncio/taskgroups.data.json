{".class": "MypyFile", "_fullname": "asyncio.taskgroups", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "AbstractEventLoop": {".class": "SymbolTableNode", "cross_ref": "asyncio.events.AbstractEventLoop", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Context": {".class": "SymbolTableNode", "cross_ref": "_contextvars.Context", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Self": {".class": "SymbolTableNode", "cross_ref": "typing.Self", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Task": {".class": "SymbolTableNode", "cross_ref": "_asyncio.Task", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TaskGroup": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "asyncio.taskgroups.TaskGroup", "name": "TaskGroup", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "asyncio.taskgroups.TaskGroup", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "asyncio.taskgroups", "mro": ["asyncio.taskgroups.TaskGroup", "builtins.object"], "names": {".class": "SymbolTable", "__aenter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "asyncio.taskgroups.TaskGroup.__aenter__", "name": "__aenter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.taskgroups.TaskGroup.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "asyncio.taskgroups.TaskGroup", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__aenter__ of TaskGroup", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.taskgroups.TaskGroup.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "asyncio.taskgroups.TaskGroup", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.taskgroups.TaskGroup.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "asyncio.taskgroups.TaskGroup", "values": [], "variance": 0}]}}}, "__aexit__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "et", "exc", "tb"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "asyncio.taskgroups.TaskGroup.__aexit__", "name": "__aexit__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "et", "exc", "tb"], "arg_types": ["asyncio.taskgroups.TaskGroup", {".class": "UnionType", "items": [{".class": "TypeType", "item": "builtins.BaseException"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.BaseException", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["types.TracebackType", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__aexit__ of TaskGroup", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_loop": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "asyncio.taskgroups.TaskGroup._loop", "name": "_loop", "type": {".class": "UnionType", "items": ["asyncio.events.AbstractEventLoop", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "_on_task_done": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "task"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "asyncio.taskgroups.TaskGroup._on_task_done", "name": "_on_task_done", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "task"], "arg_types": ["asyncio.taskgroups.TaskGroup", {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "_asyncio.Task"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_on_task_done of TaskGroup", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_tasks": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "asyncio.taskgroups.TaskGroup._tasks", "name": "_tasks", "type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "_asyncio.Task"}], "extra_attrs": null, "type_ref": "builtins.set"}}}, "create_task": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5], "arg_names": ["self", "coro", "name", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "asyncio.taskgroups.TaskGroup.create_task", "name": "create_task", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5], "arg_names": ["self", "coro", "name", "context"], "arg_types": ["asyncio.taskgroups.TaskGroup", {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.taskgroups._T", "id": -1, "name": "_T", "namespace": "asyncio.taskgroups.TaskGroup.create_task", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio._CoroutineLike"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["_contextvars.Context", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_task of TaskGroup", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.taskgroups._T", "id": -1, "name": "_T", "namespace": "asyncio.taskgroups.TaskGroup.create_task", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_asyncio.Task"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.taskgroups._T", "id": -1, "name": "_T", "namespace": "asyncio.taskgroups.TaskGroup.create_task", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.taskgroups.TaskGroup.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "asyncio.taskgroups.TaskGroup", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TracebackType": {".class": "SymbolTableNode", "cross_ref": "types.TracebackType", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_CoroutineLike": {".class": "SymbolTableNode", "cross_ref": "asyncio._CoroutineLike", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_T": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.taskgroups._T", "name": "_T", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "asyncio.taskgroups.__all__", "name": "__all__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "asyncio.taskgroups.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "asyncio.taskgroups.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "asyncio.taskgroups.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "asyncio.taskgroups.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "asyncio.taskgroups.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "asyncio.taskgroups.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "/Users/<USER>/.vscode/extensions/ms-python.mypy-type-checker-2025.2.0/bundled/libs/mypy/typeshed/stdlib/asyncio/taskgroups.pyi"}