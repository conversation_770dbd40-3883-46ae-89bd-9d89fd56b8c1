{"data_mtime": 1751863342, "dep_lines": [4, 4, 4, 4, 5, 1, 2, 3, 4, 6, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 10, 5, 10, 10, 5, 20, 5, 5, 30, 30, 30, 30], "dependencies": ["asyncio.events", "asyncio.protocols", "asyncio.streams", "asyncio.transports", "collections.abc", "subprocess", "sys", "_typeshed", "asyncio", "typing", "builtins", "_frozen_importlib", "abc", "os", "types"], "hash": "fab300eec4d90395967744e6010bc71400f361af", "id": "asyncio.subprocess", "ignore_all": true, "interface_hash": "ffde8381f47609e9a20dada9f4859a1cf8642777", "mtime": 1750496964, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/.vscode/extensions/ms-python.mypy-type-checker-2025.2.0/bundled/libs/mypy/typeshed/stdlib/asyncio/subprocess.pyi", "plugin_data": null, "size": 9301, "suppressed": [], "version_id": "1.15.0"}