# FastAPI and web framework
fastapi==0.104.1
uvicorn[standard]==0.24.0
python-multipart==0.0.6

# LangGraph and <PERSON><PERSON><PERSON><PERSON>
langgraph==0.5.1
langchain-core==0.3.68
langchain-openai==0.3.27
langchain-google-genai==2.1.6
langgraph-checkpoint-mongodb==0.1.4

# Database and persistence
motor==3.7.1
pymongo==4.12.1
beanie==1.23.6

# Authentication and security
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-decouple==3.8

# Image processing
Pillow==10.1.0
opencv-python==********

# HTTP requests and file handling
httpx==0.25.2
aiofiles==23.2.1

# Google services
google-auth==2.25.2
google-auth-oauthlib==1.1.0
google-auth-httplib2==0.1.1
google-api-python-client==2.108.0

# Streaming and async
asyncio-mqtt==0.16.1
websockets==12.0

# Development and testing
pytest==7.4.3
pytest-asyncio==0.21.1
black==23.11.0
isort==5.12.0

# Environment and configuration
pydantic==2.7.4
pydantic-settings==2.4.0

# Base64 and encoding
base64io==1.0.3

# Logging
structlog==23.2.0
