# AI Photoshot Application

A node-based AI-powered photoshot application that generates product advertisements using advanced AI models. Built with FastAPI, LangGraph, and MongoDB.

## 🎯 Overview

AI Photoshot is a visual workflow application similar to n8n that allows users to create product advertisements through a canvas-based interface. Users can drag and drop nodes, configure them with product details, and execute AI-powered workflows to generate professional product images.

## ✨ Key Features

- **🎨 Visual Canvas Interface**: Drag-and-drop node-based workflow builder like n8n
- **🤖 AI-Powered Analysis**: OpenAI GPT-4 Vision for intelligent product image analysis
- **🖼️ Smart Image Generation**: Google Gemini 2.0 Flash generates 4-5 image variants
- **👤 Flexible Modes**: Generate ads with AI human models or product-only presentations
- **☁️ Cloud Integration**: Google Drive storage for seamless file management
- **🔄 Real-time Streaming**: Live workflow execution with progress updates
- **💾 Persistent State**: MongoDB checkpointing for workflow resumption
- **🛠️ Image Editing**: Built-in editing tools (zoom, crop, rotate, upscale)

## 🏗️ Architecture

### Tech Stack
- **Backend**: FastAPI (Python 3.11+)
- **Workflow Engine**: LangGraph with MongoDB checkpointing
- **Database**: MongoDB with <PERSON><PERSON>DM
- **AI Models**:
  - OpenAI GPT-4 Vision (image analysis)
  - Google Gemini 2.0 Flash (image generation)
- **Authentication**: JWT-based security
- **Containerization**: Docker & Docker Compose
- **Image Processing**: Pillow, OpenCV

### Workflow Architecture
```
Canvas (Node Configuration) → Validation → Execution → Streaming → Results
     ↓                           ↓            ↓           ↓         ↓
  Node State                 END Node      LangGraph   Real-time  Generated
  Updates Only              Connected     Workflow     Updates    Images
```

## 🔧 Workflow Nodes

| Node | Type | Description | Configurable |
|------|------|-------------|--------------|
| **Start** | Entry | Workflow initialization | ❌ |
| **Upload Image** | Input | Product image upload (local/Drive) | ✅ |
| **Product Details** | Input | Product info, description, purpose | ✅ |
| **Mode Selection** | Input | AI model vs product-only mode | ✅ |
| **Analysis** | Processing | OpenAI image analysis | ❌ |
| **Generation** | Processing | Gemini image generation | ❌ |
| **Conversion** | Processing | Base64 to file conversion | ❌ |
| **Storage** | Output | Google Drive upload | ❌ |
| **Editing** | Output | Image editing tools | ✅ |
| **End** | Exit | Workflow completion | ❌ |

## 🚀 Quick Start

### Prerequisites
- Docker & Docker Compose
- OpenAI API key ([Get here](https://platform.openai.com/api-keys))
- Google API key ([Get here](https://aistudio.google.com/))
- Google Drive credentials (optional)

### Installation

1. **Clone the repository**
```bash
git clone <repository-url>
cd ai-photoshot
```

2. **Set up environment**
```bash
cp .env.example .env
```

3. **Configure your `.env` file** (see [Environment Setup](#-environment-setup) below)

4. **Start the application**
```bash
docker-compose up -d
```

5. **Access the services**
- 🌐 **API**: http://localhost:8000
- 📚 **API Docs**: http://localhost:8000/docs
- 🗄️ **MongoDB Express**: http://localhost:8081 (admin/admin123)
- ❤️ **Health Check**: http://localhost:8000/health

## 🔐 Environment Setup

### Required Environment Variables

Create a `.env` file with the following configuration:

```env
# Application Security
SECRET_KEY=your-super-secret-key-change-this-in-production
DEBUG=true

# Database Configuration
MONGODB_URL=*************************************************************************
DATABASE_NAME=ai_photoshot

# AI Service APIs
OPENAI_API_KEY=sk-your-openai-api-key-here
GOOGLE_API_KEY=your-google-api-key-here

# Google Drive Integration (Optional)
GOOGLE_DRIVE_CREDENTIALS_FILE=./credentials/google-drive-credentials.json

# File Upload Settings
MAX_FILE_SIZE=10485760  # 10MB
UPLOAD_DIR=uploads
```

### 🔑 How to Obtain API Keys

#### 1. **SECRET_KEY**
Generate a secure secret key:
```bash
python -c "import secrets; print(secrets.token_urlsafe(32))"
```

#### 2. **OpenAI API Key**
1. Visit [OpenAI Platform](https://platform.openai.com/)
2. Sign up/Login → **API Keys**
3. Click **"Create new secret key"**
4. Copy the key (starts with `sk-...`)
5. ⚠️ **Note**: Requires billing setup for API usage

#### 3. **Google API Key** (Gemini)
1. Go to [Google AI Studio](https://aistudio.google.com/)
2. Sign in → **Get API Key**
3. Create/select project → Copy API key
4. **Alternative**: [Google Cloud Console](https://console.cloud.google.com/) → APIs & Services → Credentials

#### 4. **Google Drive Credentials** (Optional)
1. [Google Cloud Console](https://console.cloud.google.com/) → New Project
2. Enable **Google Drive API**
3. **APIs & Services** → **Credentials** → **Create Credentials** → **Service Account**
4. Download JSON file → Place in `./credentials/` folder

## 🏃‍♂️ Development Setup

### Local Development
```bash
# Install dependencies
pip install -r requirements.txt

# Start only MongoDB
docker-compose up mongodb -d

# Run FastAPI with hot reload
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

### Testing
```bash
# Run tests
pytest

# Run with coverage
pytest --cov=app tests/
```

## 📡 API Reference & Testing

### Quick API Test
```bash
# Check if server is running
curl http://localhost:8000/health

# Get available node types (no auth required)
curl http://localhost:8000/api/v1/node-types
```

### Authentication Endpoints
```http
POST /api/v1/auth/register     # User registration
POST /api/v1/auth/login        # User login (form data)
POST /api/v1/auth/login-json   # User login (JSON)
GET  /api/v1/auth/me          # Get current user
GET  /api/v1/auth/verify-token # Verify JWT token
```

### Workflow Management
```http
GET    /api/v1/workflows                    # List user workflows
POST   /api/v1/workflows                    # Create new workflow
GET    /api/v1/workflows/{id}              # Get workflow details
PUT    /api/v1/workflows/{id}              # Update workflow (canvas)
DELETE /api/v1/workflows/{id}              # Delete workflow
POST   /api/v1/workflows/{id}/validate     # Validate before execution
```

### Node Operations
```http
POST /api/v1/workflows/{id}/nodes/{node_id}/upload-image      # Upload image
POST /api/v1/workflows/{id}/nodes/{node_id}/product-details   # Set product info
POST /api/v1/workflows/{id}/nodes/{node_id}/mode-selection    # Select mode
GET  /api/v1/workflows/{id}/nodes/{node_id}/data             # Get node data
GET  /api/v1/node-types                                      # Available node types
```

### Workflow Execution
```http
POST /api/v1/workflows/{id}/execute                          # Start execution
GET  /api/v1/workflows/{id}/execute/{exec_id}/stream        # Stream progress
GET  /api/v1/workflows/{id}/executions                      # List executions
GET  /api/v1/workflows/{id}/execute/{exec_id}               # Execution details
GET  /api/v1/workflows/{id}/execute/{exec_id}/state         # Current state
POST /api/v1/workflows/{id}/execute/{exec_id}/resume        # Resume from checkpoint
```

## 🧪 API Testing Guide

### Prerequisites for Testing
```bash
# 1. Start the application
docker-compose up -d
# OR for development
uvicorn app.main:app --reload

# 2. Verify server is running
curl http://localhost:8000/health
```

### Step 1: Authentication Testing

#### Register a New User
```bash
curl -X POST http://localhost:8000/api/v1/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "email": "<EMAIL>",
    "password": "testpassword123",
    "full_name": "Test User"
  }'
```

#### Login and Get Token
```bash
# Login with JSON
curl -X POST http://localhost:8000/api/v1/auth/login-json \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "password": "testpassword123"
  }'

# Save token for subsequent requests
TOKEN="your-jwt-token-here"
```

#### Verify Token
```bash
curl -H "Authorization: Bearer $TOKEN" \
  http://localhost:8000/api/v1/auth/me
```

### Step 2: Workflow Management Testing

#### Get Available Node Types
```bash
# No authentication required
curl http://localhost:8000/api/v1/node-types
```

#### Create a New Workflow
```bash
curl -X POST http://localhost:8000/api/v1/workflows/ \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Test Workflow",
    "description": "A test workflow for AI photoshot",
    "nodes": [],
    "edges": []
  }'
```

#### List User Workflows
```bash
curl -H "Authorization: Bearer $TOKEN" \
  http://localhost:8000/api/v1/workflows/
```

#### Update Workflow with Nodes
```bash
# Replace WORKFLOW_ID with actual ID from previous response
WORKFLOW_ID="your-workflow-id-here"

curl -X PUT http://localhost:8000/api/v1/workflows/$WORKFLOW_ID \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "nodes": [
      {
        "id": "start-1",
        "type": "start",
        "position": {"x": 100, "y": 100},
        "data": {},
        "inputs": [],
        "outputs": ["upload-1"]
      },
      {
        "id": "upload-1",
        "type": "upload_image",
        "position": {"x": 300, "y": 100},
        "data": {},
        "inputs": ["start-1"],
        "outputs": ["product-1"]
      },
      {
        "id": "product-1",
        "type": "product_details",
        "position": {"x": 500, "y": 100},
        "data": {},
        "inputs": ["upload-1"],
        "outputs": ["end-1"]
      },
      {
        "id": "end-1",
        "type": "end",
        "position": {"x": 700, "y": 100},
        "data": {},
        "inputs": ["product-1"],
        "outputs": []
      }
    ],
    "edges": [
      {"id": "edge-1", "source": "start-1", "target": "upload-1"},
      {"id": "edge-2", "source": "upload-1", "target": "product-1"},
      {"id": "edge-3", "source": "product-1", "target": "end-1"}
    ]
  }'
```

### Step 3: Node Configuration Testing

#### Upload Image to Node
```bash
# Create a test image file first
echo "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==" | base64 -d > test_image.png

# Upload to upload_image node
curl -X POST http://localhost:8000/api/v1/workflows/$WORKFLOW_ID/nodes/upload-1/upload-image \
  -H "Authorization: Bearer $TOKEN" \
  -F "file=@test_image.png"
```

#### Update Product Details
```bash
curl -X POST http://localhost:8000/api/v1/workflows/$WORKFLOW_ID/nodes/product-1/product-details \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Test Product",
    "description": "A sample product for testing",
    "purpose": "Testing AI photoshot workflow",
    "mode": "with_model",
    "additional_context": "High-quality product photography needed"
  }'
```

#### Get Node Data
```bash
curl -H "Authorization: Bearer $TOKEN" \
  http://localhost:8000/api/v1/workflows/$WORKFLOW_ID/nodes/product-1/data
```

### Step 4: Workflow Validation & Execution

#### Validate Workflow
```bash
curl -X POST http://localhost:8000/api/v1/workflows/$WORKFLOW_ID/validate \
  -H "Authorization: Bearer $TOKEN"
```

#### Start Workflow Execution
```bash
curl -X POST http://localhost:8000/api/v1/workflows/$WORKFLOW_ID/execute \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "workflow_id": "'$WORKFLOW_ID'",
    "input_data": {"test": "data"}
  }'
```

#### List Workflow Executions
```bash
curl -H "Authorization: Bearer $TOKEN" \
  http://localhost:8000/api/v1/workflows/$WORKFLOW_ID/executions
```

#### Stream Execution Progress (Server-Sent Events)
```bash
# Replace EXECUTION_ID with actual execution ID
EXECUTION_ID="your-execution-id-here"

curl -H "Authorization: Bearer $TOKEN" \
  http://localhost:8000/api/v1/workflows/$WORKFLOW_ID/execute/$EXECUTION_ID/stream
```

### Step 5: Complete Test Script

Here's a complete test script that runs all the above tests:

```bash
#!/bin/bash

# Set base URL
BASE_URL="http://localhost:8000"

echo "🧪 Starting AI Photoshot API Tests..."

# 1. Health Check
echo "1. Testing health endpoint..."
curl -s $BASE_URL/health | jq .

# 2. Register User
echo "2. Registering test user..."
curl -s -X POST $BASE_URL/api/v1/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "email": "<EMAIL>",
    "password": "testpassword123",
    "full_name": "Test User"
  }' | jq .

# 3. Login and get token
echo "3. Logging in..."
TOKEN=$(curl -s -X POST $BASE_URL/api/v1/auth/login-json \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "password": "testpassword123"
  }' | jq -r '.access_token')

echo "Token: $TOKEN"

# 4. Create workflow
echo "4. Creating workflow..."
WORKFLOW_ID=$(curl -s -X POST $BASE_URL/api/v1/workflows/ \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Test Workflow",
    "description": "A test workflow for AI photoshot",
    "nodes": [],
    "edges": []
  }' | jq -r '.id')

echo "Workflow ID: $WORKFLOW_ID"

# 5. Update workflow with nodes
echo "5. Adding nodes to workflow..."
curl -s -X PUT $BASE_URL/api/v1/workflows/$WORKFLOW_ID \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "nodes": [
      {"id": "start-1", "type": "start", "position": {"x": 100, "y": 100}, "data": {}, "inputs": [], "outputs": ["upload-1"]},
      {"id": "upload-1", "type": "upload_image", "position": {"x": 300, "y": 100}, "data": {}, "inputs": ["start-1"], "outputs": ["end-1"]},
      {"id": "end-1", "type": "end", "position": {"x": 500, "y": 100}, "data": {}, "inputs": ["upload-1"], "outputs": []}
    ],
    "edges": [
      {"id": "edge-1", "source": "start-1", "target": "upload-1"},
      {"id": "edge-2", "source": "upload-1", "target": "end-1"}
    ]
  }' | jq .

# 6. Validate workflow
echo "6. Validating workflow..."
curl -s -X POST $BASE_URL/api/v1/workflows/$WORKFLOW_ID/validate \
  -H "Authorization: Bearer $TOKEN" | jq .

# 7. Start execution
echo "7. Starting workflow execution..."
EXECUTION_ID=$(curl -s -X POST $BASE_URL/api/v1/workflows/$WORKFLOW_ID/execute \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"workflow_id": "'$WORKFLOW_ID'", "input_data": {"test": "data"}}' | jq -r '.id')

echo "Execution ID: $EXECUTION_ID"

# 8. List executions
echo "8. Listing executions..."
curl -s -H "Authorization: Bearer $TOKEN" \
  $BASE_URL/api/v1/workflows/$WORKFLOW_ID/executions | jq .

echo "✅ All tests completed!"
```

### Expected Response Formats

#### Successful Authentication Response
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "bearer"
}
```

#### Workflow Creation Response
```json
{
  "id": "686b287178a94728d17fe002",
  "name": "Test Workflow",
  "description": "A test workflow for AI photoshot",
  "user_id": "686b283d78a94728d17fe001",
  "nodes": [...],
  "edges": [...],
  "created_at": "2025-07-07T01:52:49.711000",
  "updated_at": "2025-07-07T01:52:49.711000"
}
```

#### Workflow Validation Response
```json
{
  "valid": true,
  "errors": [],
  "warnings": ["Missing recommended node: mode_selection"],
  "can_execute": true
}
```

#### Execution Start Response
```json
{
  "id": "686b2ae13f7968159ddd076d",
  "workflow_id": "686b287178a94728d17fe002",
  "user_id": "686b283d78a94728d17fe001",
  "status": "pending",
  "input_data": {"test": "data"},
  "output_data": {},
  "error_message": null,
  "started_at": "2025-07-07T02:03:13.350000",
  "completed_at": null,
  "langgraph_thread_id": "d5a34498-4b07-4e78-91f0-a4bb9c912284"
}
```

### Common Error Responses

#### Authentication Error
```json
{
  "detail": "Could not validate credentials"
}
```

#### Validation Error
```json
{
  "valid": false,
  "errors": ["END node must be connected to execute workflow"],
  "warnings": [],
  "can_execute": false
}
```

#### Not Found Error
```json
{
  "detail": "Workflow not found"
}
```

### Troubleshooting

#### Server Not Starting
```bash
# Check if port 8000 is available
netstat -an | grep 8000

# Check application logs
docker-compose logs api

# For development mode
python -m uvicorn app.main:app --reload --log-level debug
```

#### Database Connection Issues
```bash
# Check MongoDB status
docker-compose ps mongodb

# Check database logs
docker-compose logs mongodb

# Test database connection
curl http://localhost:8000/health
```

#### Authentication Issues
```bash
# Verify token format
echo $TOKEN | cut -d'.' -f2 | base64 -d

# Check token expiration
curl -H "Authorization: Bearer $TOKEN" http://localhost:8000/api/v1/auth/verify-token
```

#### File Upload Issues
```bash
# Check file size (max 10MB)
ls -lh test_image.png

# Verify file type
file test_image.png

# Check uploads directory permissions
ls -la uploads/
```

### Performance Testing

#### Load Testing with Apache Bench
```bash
# Test authentication endpoint
ab -n 100 -c 10 -p login.json -T application/json \
  http://localhost:8000/api/v1/auth/login-json

# Test workflow listing (with auth header)
ab -n 100 -c 10 -H "Authorization: Bearer $TOKEN" \
  http://localhost:8000/api/v1/workflows/
```

#### Stress Testing Workflow Execution
```bash
# Create multiple concurrent executions
for i in {1..5}; do
  curl -X POST http://localhost:8000/api/v1/workflows/$WORKFLOW_ID/execute \
    -H "Authorization: Bearer $TOKEN" \
    -H "Content-Type: application/json" \
    -d '{"workflow_id": "'$WORKFLOW_ID'", "input_data": {"test": "data'$i'"}}' &
done
wait
```

### API Documentation

#### Interactive API Documentation
- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc
- **OpenAPI JSON**: http://localhost:8000/openapi.json

#### Postman Collection
You can import the OpenAPI specification into Postman:
1. Open Postman
2. Click "Import"
3. Enter URL: `http://localhost:8000/openapi.json`
4. Configure authentication with Bearer token

## 📁 Project Structure

```
ai-photoshot/
├── app/
│   ├── __init__.py
│   ├── main.py                     # FastAPI application entry
│   ├── api/                        # API route handlers
│   │   ├── auth.py                # Authentication endpoints
│   │   ├── workflow.py            # Workflow management
│   │   ├── execution.py           # Workflow execution
│   │   └── nodes.py               # Node operations
│   ├── core/                       # Core application logic
│   │   ├── config.py              # Settings and configuration
│   │   ├── database.py            # MongoDB connection
│   │   ├── security.py            # JWT and password handling
│   │   └── dependencies.py        # FastAPI dependencies
│   ├── models/                     # Pydantic data models
│   │   ├── user.py                # User models
│   │   ├── workflow.py            # Workflow and execution models
│   │   ├── node.py                # Node execution models
│   │   └── image.py               # Image and metadata models
│   ├── workflows/                  # LangGraph workflow definitions
│   │   ├── photoshot_workflow.py  # Main workflow logic
│   │   ├── nodes/                 # Individual node implementations
│   │   └── checkpoints/           # MongoDB checkpointing
│   │       └── mongodb_saver.py   # LangGraph MongoDB integration
│   └── services/                   # External service integrations
│       ├── workflow_service.py    # Workflow execution service
│       ├── openai_service.py      # OpenAI API integration
│       ├── gemini_service.py      # Google Gemini integration
│       └── storage_service.py     # Google Drive integration
├── tests/                          # Test suite
├── uploads/                        # Local file storage
├── credentials/                    # Service account credentials
├── docker-compose.yml             # Multi-service Docker setup
├── Dockerfile                     # Application container
├── requirements.txt               # Python dependencies
├── .env.example                   # Environment template
├── .gitignore                     # Git ignore rules
└── README.md                      # This file
```

## 🔄 Workflow Execution Flow

### Canvas Phase (No Processing)
1. **Create Workflow**: User creates a new workflow canvas
2. **Add Nodes**: Drag and drop nodes from the palette
3. **Configure Nodes**:
   - Upload product images
   - Set product details (name, description, purpose)
   - Select advertisement mode (with/without AI model)
4. **Connect Nodes**: Draw connections between nodes
5. **Connect to END**: Must connect workflow to END node for execution

### Execution Phase (Only When END Connected)
1. **Validation**: System checks if END node is connected
2. **Execution Start**: User clicks "RUN" button
3. **LangGraph Processing**:
   - START → UPLOAD_IMAGE → PRODUCT_DETAILS → MODE_SELECTION
   - ANALYSIS (OpenAI) → GENERATION (Gemini) → CONVERSION
   - STORAGE (Google Drive) → EDITING → END
4. **Real-time Updates**: Streaming progress via WebSocket/SSE
5. **Checkpointing**: Each step saved to MongoDB for resumption

## 🛠️ Development Workflow

### Current Status
- ✅ **Core Architecture**: FastAPI + LangGraph + MongoDB
- ✅ **Authentication System**: JWT-based user management
- ✅ **Workflow Management**: CRUD operations for workflows
- ✅ **Node System**: Canvas-based node configuration
- ✅ **Execution Engine**: LangGraph with MongoDB checkpointing
- ✅ **AI Services**: OpenAI analysis + Gemini generation
- 🔄 **Image Processing**: Basic upload/conversion (needs editing tools)
- 🔄 **Google Drive**: Storage integration (needs implementation)
- 🔄 **Frontend**: Canvas interface (needs React/Vue implementation)

### Next Steps
1. **Complete Image Editing Service**: Implement zoom, crop, rotate, upscale
2. **Google Drive Integration**: File upload/download service
3. **Frontend Canvas**: React-based node editor (like n8n)
4. **Testing Suite**: Comprehensive API and workflow tests
5. **Production Deployment**: Kubernetes/Docker Swarm setup

## 🔒 Security Considerations

- **API Keys**: Never commit to version control
- **JWT Tokens**: Secure secret key and proper expiration
- **File Uploads**: Validate file types and sizes
- **Rate Limiting**: Implement API rate limits
- **CORS**: Configure for production domains
- **MongoDB**: Use strong credentials and network security

## 📊 Monitoring & Logging

- **Structured Logging**: JSON-formatted logs with structlog
- **Health Checks**: Built-in health endpoints
- **API Metrics**: Request/response monitoring
- **Workflow Tracking**: Execution state and performance
- **Error Handling**: Comprehensive exception management

## 🤝 Contributing

1. **Fork** the repository
2. **Create** a feature branch (`git checkout -b feature/amazing-feature`)
3. **Commit** your changes (`git commit -m 'Add amazing feature'`)
4. **Push** to the branch (`git push origin feature/amazing-feature`)
5. **Open** a Pull Request

### Development Guidelines
- Follow **PEP 8** Python style guide
- Add **type hints** for all functions
- Write **comprehensive tests** for new features
- Update **documentation** for API changes
- Use **conventional commits** for commit messages

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **LangGraph**: For the powerful workflow engine
- **FastAPI**: For the high-performance web framework
- **OpenAI**: For advanced image analysis capabilities
- **Google Gemini**: For state-of-the-art image generation
- **MongoDB**: For reliable data persistence

---

**Built with ❤️ for the AI community**

For questions, issues, or contributions, please visit our [GitHub repository](https://github.com/your-username/ai-photoshot).
