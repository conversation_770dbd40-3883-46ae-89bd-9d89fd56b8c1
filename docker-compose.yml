version: '3.8'

services:
  # MongoDB database
  mongodb:
    image: mongo:7.0
    container_name: ai_photoshot_mongodb
    restart: unless-stopped
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: password123
      MONGO_INITDB_DATABASE: ai_photoshot
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
      - ./mongo-init:/docker-entrypoint-initdb.d
    networks:
      - ai_photoshot_network

  # FastAPI application
  api:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: ai_photoshot_api
    restart: unless-stopped
    environment:
      - MONGODB_URL=*****************************************/ai_photoshot?authSource=admin
      - DATABASE_NAME=ai_photoshot
      - SECRET_KEY=your-secret-key-change-in-production
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - GOOGLE_API_KEY=${GOOGLE_API_KEY}
      - DEBUG=true
    ports:
      - "8000:8000"
    volumes:
      - ./uploads:/app/uploads
      - ./app:/app/app  # For development hot reload
    depends_on:
      - mongodb
    networks:
      - ai_photoshot_network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # MongoDB Express (optional - for database management)
  mongo-express:
    image: mongo-express:1.0.0
    container_name: ai_photoshot_mongo_express
    restart: unless-stopped
    environment:
      ME_CONFIG_MONGODB_ADMINUSERNAME: admin
      ME_CONFIG_MONGODB_ADMINPASSWORD: password123
      ME_CONFIG_MONGODB_URL: *****************************************/
      ME_CONFIG_BASICAUTH_USERNAME: admin
      ME_CONFIG_BASICAUTH_PASSWORD: admin123
    ports:
      - "8081:8081"
    depends_on:
      - mongodb
    networks:
      - ai_photoshot_network
    profiles:
      - tools

  # Redis (for caching and session management)
  redis:
    image: redis:7.2-alpine
    container_name: ai_photoshot_redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - ai_photoshot_network
    command: redis-server --appendonly yes
    profiles:
      - cache

volumes:
  mongodb_data:
    driver: local
  redis_data:
    driver: local

networks:
  ai_photoshot_network:
    driver: bridge
